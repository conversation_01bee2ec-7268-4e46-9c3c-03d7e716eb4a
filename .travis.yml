language: android
jdk: oraclejdk8

branches:
  only:
    - master
    - develop
android:
  components:
    #Needed to build your project with the SDK Platform Tools revision 24 or above
    - tools
    - platform-tools
    #tools section appears twice on purpose as it’s required to get the newest Android SDK tools
    - tools

    # The BuildTools version used by your project
    - build-tools-27.0.3

    # The SDK version used to compile your project
    - android-27

    # Additional components
    - extra-google-google_play_services
    - extra-google-m2repository
    - extra-android-m2repository
    - addon-google_apis-google-27
    - extra-android-support


licenses:
  - android-sdk-preview-license-.+
  - android-sdk-license-.+
  - google-gdk-license-.+
script:
  - ./gradlew build

#recommended by travis Caching strategy
before_cache:
  - rm -f  $HOME/.gradle/caches/modules-2/modules-2.lock
  - rm -fr $HOME/.gradle/caches/*/plugin-resolution/

before_install:
- openssl aes-256-cbc -K $encrypted_e62f48a32e64_key -iv $encrypted_e62f48a32e64_iv -in keystore.enc -out keystore -d

cache:
  directories:
    - $HOME/.gradle/caches/
    - $HOME/.gradle/wrapper/
    - $HOME/.android/build-cache

after_failure:
  - reports-publisher

after_success:
  - reports-publisher

#QG Notification Webhook  
notifications:
  webhooks: https://devhose.mpi-internal.com/devhose/travis
