[versions]
kotlin = "2.0.21"
jvmTarget = "17"
compileSdk = "35"
targetSdk = "35"
minSdk = "21"
versionCode = "1498"
versionName = "9.9.0"
composeCompiler = "1.5.14"
tiktok-sdk = "1.3.8"
agp = "8.7.0"
googleServices = "4.4.2"
devtoolsKsp = "2.0.21-1.0.28"
firebasePerfPlugin = "1.4.2"
firebaseCrashlytricsPlugin = "2.9.9"
apollo3 = "3.6.2"
wire = "5.0.0-alpha03"
desugarJdk = "2.0.4"
coreKtx = "1.9.0"
splashscreen = "1.0.1"
multidex = "2.0.1"
legacySupport = "1.0.0"
appCompat = "1.6.1"
fragment = "1.6.2"
constraintlayout = "2.1.4"
browser = "1.8.0"
material = "1.12.0"
flexbox = "3.0.0"
calendarView = "1.0.3"
imagePicker = "3.0.0"
glide = "4.12.0"
touchImageView = "3.0.1"
shortcutBadger = "1.1.19"
shimmer = "0.5.0"
lifecycle = "2.7.0"
paging = "3.3.2"
hilt = "2.51.1"
hiltWork = "1.2.0"
hiltCompiler = "1.2.0"
firebaseBom = "32.8.1"
firebaseCore = "21.1.1"
firebaseInvites = "17.0.0"
firebaseAuthKts = "21.1.0"
gmsBase = "18.4.0"
gmsAds = "23.0.0"
gmsAuth = "21.1.0"
gmsLocation = "21.2.0"
gmsAnalytics = "18.0.4"
gmsPlaces = "17.0.0"
gmsMaps = "18.2.0"
appReview = "2.0.1"
appUpdate = "2.1.0"
exoplayer = "2.18.0"
bouquet = "1.1.2"
zoomable = "1.5.1"
googleAdsMediation = "6.17.0.0"
androidxCredentials = "1.2.2"
googleid = "1.1.1"
billing = "8.0.0"
retrofit = "2.9.0"
okhttpBom = "4.12.0"
work = "2.9.0"
preference = "1.2.0"
room = "2.6.1"
dataStore = "1.1.1"
rxJava = "2.2.10"
rxAndroid = "2.1.1"
facebookSdk = "16.0.0"
facebookAudience = "6.12.0"
facebookAnnotation = "0.18.0"
androidxMacroBenchmark = "1.2.4"
clarity-compose = "3.3.1"


#compose versions
androidxActivity = "1.9.2"
composeBom = "2024.09.03"
navigation = "2.9.3"
hiltNavigationCompose = "1.2.0"
collectionsImmutable = "0.3.8"
constraintlayout_compose = "1.1.1"
coil = "2.7.0"
charts = "1.0.0"
webview = "1.9.20"
palette="1.0.0"

installreferrer = "2.2"
appboy = "24.3.0"
eventbus = "3.3.1"
tapTargetPrompt = "2.6.0"
androidyoutubeplayer = "11.0.0"
viewpropertyobjectanimator = "1.4.5"
easypermissions = "3.0.0"
androidChart = "v3.1.0"
compressor = "3.0.1"

junit = "4.13.2"
robolectric = "4.7.3"
mockk = "1.13.5"
coroutines = "1.7.3"
datetime = "0.6.1"
serialization = "1.8.1"
junitVersion = "1.2.1"
espressoCore = "3.6.0"
uiautomator = "2.3.0"
benchmarkMacroJunit4 = "1.2.4"
moduleGraph = "2.5.0"
lifecycleRuntimeKtx = "2.8.5"
pagingCommonAndroid = "3.3.2"
uiTextAndroid = "1.7.5"
coreKtxVersion = "1.15.0"
firebaseConfigKtx = "22.1.0"
playServicesAdsLite = "23.0.0"
foundationAndroid = "1.8.1"
material3Android = "1.3.2"

[libraries]

#clarity
microsoft-clarity = { module = "com.microsoft.clarity:clarity-compose", version.ref = "clarity-compose" }

#desugar
desugar_jdk_libs = { module = "com.android.tools:desugar_jdk_libs", version.ref = "desugarJdk" }

#core
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "splashscreen" }

#presentation
androidx-multidex = { group = "androidx.multidex", name = "multidex", version.ref = "multidex" }
androidx-legacy-support = { module = "androidx.legacy:legacy-support-v4", version.ref = "legacySupport" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appCompat" }
androidx-appcompat-resources = { group = "androidx.appcompat", name = "appcompat-resources", version.ref = "appCompat" }
androidx-fragment = { group = "androidx.fragment", name = "fragment-ktx", version.ref = "fragment" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-browser = { group = "androidx.browser", name = "browser", version.ref = "browser" }
androidx-lifecycle-livedata = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
androidx-lifecycle-process = { group = "androidx.lifecycle", name = "lifecycle-process", version.ref = "lifecycle" }
androidx-lifecycle-common-java8 = { group = "androidx.lifecycle", name = "lifecycle-common-java8", version.ref = "lifecycle" }
androidx-paging = { group = "androidx.paging", name = "paging-runtime", version.ref = "paging" }
com-google-material = { group = "com.google.android.material", name = "material", version.ref = "material" }
com-google-flexbox = { group = "com.google.android.flexbox", name = "flexbox", version.ref = "flexbox" }
com-github-kizitonwose = { group = "com.github.kizitonwose", name = "CalendarView", version.ref = "calendarView" }
com-github-image-picker = { group = "com.github.esafirm", name = "android-image-picker", version.ref = "imagePicker" }
com-github-glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }
com-github-glide-compiler = { group = "com.github.bumptech.glide", name = "compiler", version.ref = "glide" }
com-github-touchImageView = { group = "com.github.MikeOrtiz", name = "TouchImageView", version.ref = "touchImageView" }
com-facebook-shimmer = { group = "com.facebook.shimmer", name = "shimmer", version.ref = "shimmer" }
me-leolin = { group = "me.leolin", name = "ShortcutBadger", version.ref = "shortcutBadger" }

#compose
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "androidxActivity" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-compose-runtime = { group = "androidx.compose.runtime", name = "runtime" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-navigation = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation" }
androidx-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "hiltNavigationCompose" }
androidx-paging-compose = { group = "androidx.paging", name = "paging-compose", version.ref = "paging" }
coil-kt = { group = "io.coil-kt", name = "coil", version.ref = "coil" }
coil-compose = { module = "io.coil-kt:coil-compose", version.ref = "coil" }
coil-svg = { module = "io.coil-kt:coil-svg", version.ref = "coil" }
coil-video = { module = "io.coil-kt:coil-video", version.ref = "coil" }
coil-gif = { module = "io.coil-kt:coil-gif", version.ref = "coil" }
constraintlayout-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "constraintlayout_compose" }
kotlinx-collections-immutable = { group = "org.jetbrains.kotlinx", name = "kotlinx-collections-immutable", version.ref = "collectionsImmutable" }
charts = { group = "com.netguru", name = "charts", version.ref = "charts" }
kevinnzou-webview = { group = "io.github.kevinnzou", name = "compose-webview-multiplatform", version.ref = "webview" }
androidx-palette = { group = "androidx.palette", name = "palette-ktx", version.ref = "palette" }

#kotlinx
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "coroutines" }
kotlinx-datetime = { group = "org.jetbrains.kotlinx", name = "kotlinx-datetime", version.ref = "datetime" }
kotlinx-serialization = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "serialization" }

#Firebase
com-google-firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBom" }
com-google-firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging-ktx" }
com-google-firebase-config = { group = "com.google.firebase", name = "firebase-config-ktx" }
com-google-firebase-config-ktx = { group = "com.google.firebase", name = "firebase-config-ktx", version.ref = "firebaseConfigKtx" }
com-google-firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
com-google-firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
com-google-firebase-perf = { group = "com.google.firebase", name = "firebase-perf-ktx" }
com-google-firebase-core = { group = "com.google.firebase", name = "firebase-core", version.ref = "firebaseCore" }
com-google-firebase-invites = { group = "com.google.firebase", name = "firebase-invites", version.ref = "firebaseInvites" }
com-google-firebase-auth-ktx = { group = "com.google.firebase", name = "firebase-auth-ktx", version.ref = "firebaseAuthKts" }
firebase-dynamic-links = { module = "com.google.firebase:firebase-dynamic-links" }

#Google
com-google-android-gms-base = { group = "com.google.android.gms", name = "play-services-base", version.ref = "gmsBase" }
com-google-android-gms-ads = { group = "com.google.android.gms", name = "play-services-ads", version.ref = "gmsAds" }
com-google-android-gms-auth = { group = "com.google.android.gms", name = "play-services-auth", version.ref = "gmsAuth" }
com-google-android-gms-location = { group = "com.google.android.gms", name = "play-services-location", version.ref = "gmsLocation" }
com-google-android-gms-analytics = { group = "com.google.android.gms", name = "play-services-analytics", version.ref = "gmsAnalytics" }
com-google-android-gms-places = { group = "com.google.android.gms", name = "play-services-places", version.ref = "gmsPlaces" }
com-google-android-gms-maps = { group = "com.google.android.gms", name = "play-services-maps", version.ref = "gmsMaps" }
com-google-android-play-review = { group = "com.google.android.play", name = "review", version.ref = "appReview" }
com-google-android-play-update = { group = "com.google.android.play", name = "app-update", version.ref = "appUpdate" }
com-google-android-exoplayer = { group = "com.google.android.exoplayer", name = "exoplayer", version.ref = "exoplayer" }
com-google-ads-mediation = { group = "com.google.ads.mediation", name = "facebook", version.ref = "googleAdsMediation" }
com-google-android-billing = { group = "com.android.billingclient", name = "billing", version.ref = "billing" }
androidx-credentials = { group = "androidx.credentials", name = "credentials", version.ref = "androidxCredentials" }
androidx-credentials-play-services-auth = { group = "androidx.credentials", name = "credentials-play-services-auth", version.ref = "androidxCredentials" }
com-google-googleid = { module = "com.google.android.libraries.identity.googleid:googleid", version.ref = "googleid" }

#hilt
com-google-hilt = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
com-google-hilt-compiler = { group = "com.google.dagger", name = "hilt-android-compiler", version.ref = "hilt" }
androidx-work = { group = "androidx.work", name = "work-runtime", version.ref = "work" }
androidx-hilt-work = { module = "androidx.hilt:hilt-work", version.ref = "hiltWork" }
androidx-hilt-compiler = { group = "androidx.hilt", name = "hilt-compiler", version.ref = "hiltCompiler" }

#Facebook
com-facebook-sdk = { group = "com.facebook.android", name = "facebook-android-sdk", version.ref = "facebookSdk" }
com-facebook-audience = { group = "com.facebook.android", name = "audience-network-sdk", version.ref = "facebookAudience" }
com-facebook-annotation = { group = "com.facebook.infer.annotation", name = "infer-annotation", version.ref = "facebookAnnotation" }

#network
com-squareup-retrofit2-retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
com-squareup-retrofit2-retrofit-mock = { group = "com.squareup.retrofit2", name = "retrofit-mock", version.ref = "retrofit" }
com-squareup-retrofit2-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }
com-squareup-retrofit2-adapter-rxjava2 = { group = "com.squareup.retrofit2", name = "adapter-rxjava2", version.ref = "retrofit" }
com-squareup-okhttp3-okhttpBom = { group = "com.squareup.okhttp3", name = "okhttp-bom", version.ref = "okhttpBom" }
com-squareup-okhttp3-logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor" }
com-squareup-okhttp3-okhttp = { group = "com.squareup.okhttp3", name = "okhttp" }
com-squareup-okhttp3-mockwebserver = { group = "com.squareup.okhttp3", name = "mockwebserver" }
apollo3-runtime = { group = "com.apollographql.apollo3", name = "apollo-runtime", version.ref = "apollo3" }
apollo3-normalized-cache = { group = "com.apollographql.apollo3", name = "apollo-normalized-cache", version.ref = "apollo3" }

#local
androidx-preference = { group = "androidx.preference", name = "preference-ktx", version.ref = "preference" }
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-datastore = { group = "androidx.datastore", name = "datastore", version.ref = "dataStore" }
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "dataStore" }

#RxJava2
io-rxjava2-rxjava = { group = "io.reactivex.rxjava2", name = "rxjava", version.ref = "rxJava" }
io-rxjava2-rxandroid = { group = "io.reactivex.rxjava2", name = "rxandroid", version.ref = "rxAndroid" }

#Other
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer" }
appBoy = { module = "com.appboy:android-sdk-ui", version.ref = "appboy" }
eventBus = { module = "org.greenrobot:eventbus", version.ref = "eventbus" }
tap-target-promt = { module = "uk.co.samuelwall:material-tap-target-prompt", version.ref = "tapTargetPrompt" }
androidyoutubeplayer = { module = "com.pierfrancescosoffritti.androidyoutubeplayer:core", version.ref = "androidyoutubeplayer" }
viewpropertyobjectanimator = { module = "com.bartoszlipinski:viewpropertyobjectanimator", version.ref = "viewpropertyobjectanimator" }
easypermissions = { module = "pub.devrel:easypermissions", version.ref = "easypermissions" }
androidChart = { module = "com.github.PhilJay:MPAndroidChart", version.ref = "androidChart" }
compressor = { module = "id.zelory:compressor", version.ref = "compressor" }
bouquet = { module = "io.github.grizzi91:bouquet", version.ref = "bouquet" }
zoomable = { module = "net.engawapg.lib:zoomable", version.ref = "zoomable" }

#Test
junit = { module = "junit:junit", version.ref = "junit" }
robolectric = { module = "org.robolectric:robolectric", version.ref = "robolectric" }
mockk = { module = "io.mockk:mockk", version.ref = "mockk" }
coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "coroutines" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-uiautomator = { group = "androidx.test.uiautomator", name = "uiautomator", version.ref = "uiautomator" }
androidx-benchmark-macro-junit4 = { group = "androidx.benchmark", name = "benchmark-macro-junit4", version.ref = "benchmarkMacroJunit4" }
androidx-compose-ui-test = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-compose-ui-testManifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }

# Dependencies of the included build-logic
android-gradle-plugin = { module = "com.android.tools.build:gradle", version.ref = "agp" }
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }

androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-paging-common-android = { group = "androidx.paging", name = "paging-common-android", version.ref = "pagingCommonAndroid" }
androidx-ui-text-android = { group = "androidx.compose.ui", name = "ui-text-android", version.ref = "uiTextAndroid" }
core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtxVersion" }
play-services-ads-lite = { group = "com.google.android.gms", name = "play-services-ads-lite", version.ref = "playServicesAdsLite" }
androidx-foundation-android = { group = "androidx.compose.foundation", name = "foundation-android", version.ref = "foundationAndroid" }
androidx-material3-android = { group = "androidx.compose.material3", name = "material3-android", version.ref = "material3Android" }


#Tiktok SDK
tiktok-business = { module = "com.github.tiktok:tiktok-business-android-sdk", version.ref = "tiktok-sdk" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
google-services = { id = "com.google.gms.google-services", version.ref = "googleServices" }
google-devtools-ksp = { id = "com.google.devtools.ksp", version.ref = "devtoolsKsp" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
firebase-perf = { id = "com.google.firebase.firebase-perf", version.ref = "firebasePerfPlugin" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlytricsPlugin" }
apollo3 = { id = "com.apollographql.apollo3", version.ref = "apollo3" }
android-test = { id = "com.android.test", version.ref = "agp" }
baselineprofile = { id = "androidx.baselineprofile", version.ref = "androidxMacroBenchmark" }
module-graph = { id = "com.jraska.module.graph.assertion", version.ref = "moduleGraph" }
wire = { id = "com.squareup.wire", version.ref = "wire" }
room = { id = "androidx.room", version.ref = "room" }