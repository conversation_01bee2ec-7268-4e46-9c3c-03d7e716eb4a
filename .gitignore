# Built application files
*.apk
*.aab
*.ap_
*.dex
*.class
bin/
gen/
out/
build/
captures/
.externalNativeBuild/
.cxx/
*.hprof

# Feature module build directories
feature/messagingui/build/
feature/messagingui/build/**
feature/*/build/
feature/*/build/**

# Kotlin generated files
**/build/kotlin/
**/build/kapt/
**/build/tmp/
**/build/generated/
**/build/intermediates/
**/build/outputs/
**/build/reports/
**/build/test-results/
**/build/kotlin/kaptGenerateStubs*/
**/build/kotlin/compile*/
**/build/kotlin/cacheable/
**/build/kotlin/caches-jvm/
**/build/kotlin/jvm/
**/build/kotlin/proto.tab
**/build/kotlin/*.tab
**/build/kotlin/*.bin
**/build/kotlin/*.lock
**/build/kotlin/*.log

# Specific Kotlin compile directories
feature/messagingui/build/kotlin/compileDebugKotlin/
feature/messagingui/build/kotlin/compileDebugKotlin/**

# Kotlin cache files
**/build/kotlin/kaptGenerateStubsDebugKotlin/
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/*.tab
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/*.bin
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/*.lock
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/*.log
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/name-to-source.tab
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-name.tab
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts-bin.tab
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts-lock.tab
**/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts-log.tab

# Kotlin local state files
**/build/kotlin/kaptGenerateStubsDebugKotlin/local-state/
**/build/kotlin/kaptGenerateStubsDebugKotlin/local-state/*
**/build/kotlin/kaptGenerateStubsDebugKotlin/local-state/**/*
**/build/kotlin/kaptGenerateStubsDebugKotlin/local-state/**/**/*
**/build/kotlin/kaptGenerateStubsDebugKotlin/local-state/**/**/**/*

# Gradle files
.gradle/
build/
gradle-app.setting
!gradle-wrapper.jar
.gradletasknamecache

# Local configuration file
local.properties

# Android Studio
*.iml
.idea/
.navigation/
output.json
*.ipr
*.iws
.idea/workspace.xml
.idea/tasks.xml
.idea/gradle.xml
.idea/assetWizardSettings.xml
.idea/dictionaries
.idea/libraries
.idea/caches
.idea/modules.xml
.idea/navEditor.xml
.idea/vcs.xml
.idea/jsLibraryMappings.xml
.idea/datasources.xml
.idea/dataSources.ids
.idea/sqlDataSources.xml
.idea/dynamic.xml
.idea/uiDesigner.xml
.idea/jarRepositories.xml

# Keystore files
*.jks
*.keystore
!debug.keystore

# External native build folder
.externalNativeBuild
.cxx/

# Version control
vcs.xml

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Generated files
*.log
*.logcat
*.hprof
*.orig
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# Android Profiling
*.hprof

# Backup files
*.bak
*.tmp
*.temp

# Generated source files
generated/
generated_src/
generated_res/

# Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# Project specific
/projectFilesBackup/
/keystore/
app/debug/
app/release/
app/avito/
libraries/*/build/

# Module specific build folders
**/build/
**/build/*
**/build/**/*
**/build/**/**/*
**/build/**/**/**/*
**/build/**/**/**/**/*
**/build/**/**/**/**/**/*

# Java class files
*.class

# Crashlytics configuations
com_crashlytics_export_strings.xml

# Signing files
.signing/

# User-specific configurations
.idea/libraries/
.idea/workspace.xml
.idea/tasks.xml
.idea/.name
.idea/compiler.xml
.idea/copyright/profiles_settings.xml
.idea/encodings.xml
.idea/misc.xml
.idea/modules.xml
.idea/scopes/scope_settings.xml
.idea/vcs.xml

### INTELLIJ ###
NextgenAndroidApp/NextgenAndroidApp.iml
*.iml
#*.ipr
#*.iws
.idea

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

#Librairie Files 

libraries/photoview/build/
libraries/viewPagerIndicator/build/
libraries/library/build/
libraries/FloatLabeledEditText/build/

# Local configuration file (sdk path, etc)
local.properties

/projectFilesBackup/
/build/
/captures/
captures/
libraries/viewPagerIndicator/
/captures/*
app/avito/*
app/avito/
app/release
/keystore
app/debug/
release/
debug/

# Ignore kaptGenerateStubsDebugKotlin build output
feature/messagingui/build/kotlin/kaptGenerateStubsDebugKotlin/

/feature/messagingui/build/
