package se.scmv.morocco.ad.ad_view.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.AdDetails

/** Displays similar ads in a horizontally scrolling list. */
@Composable
fun SimilarAdsSection(
    similarAds: List<AdDetails.SimilarAd>,
    isShop: Boolean = false,
    isConnectedShop: Boolean,
    navigateToSimilarAd: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .padding(horizontal = MaterialTheme.dimens.default)
    ) {
        Text(
            text = if (isShop) stringResource(R.string.other_ads_from_shop) else stringResource(R.string.suggested_ads),
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(bottom = MaterialTheme.dimens.regular)
        )

        LazyRow {
            items(similarAds) { similarAd ->
                Card(
                    shape = RoundedCornerShape(MaterialTheme.dimens.small), // Apply the same shape as the image
                    modifier = Modifier
                        .padding(end = MaterialTheme.dimens.regular)
                        .width(130.dp)
                        .background(color = MaterialTheme.colorScheme.surface)
                        .clickable { navigateToSimilarAd(similarAd.listId) }
                ) {
                    Column(
                        horizontalAlignment = Alignment.Start,
                        modifier = Modifier.background(color = MaterialTheme.colorScheme.surface)
                    ) {

                        Box(
                            modifier = Modifier
                                .width(135.dp)
                                .height(166.dp)
                                .shadow(
                                    1.dp,
                                    shape = RoundedCornerShape(MaterialTheme.dimens.betweenSmallMedium)
                                )
                                .clip(RoundedCornerShape(MaterialTheme.dimens.betweenSmallMedium))
                        ) {
                            // Main Image
                            AsyncImage(
                                model = similarAd.media?.defaultImage?.paths?.standard,
                                placeholder = painterResource(R.drawable.ic_no_image),
                                contentDescription = null,
                                modifier = Modifier
                                    .size(135.dp, 166.dp)
                                    .padding(0.dp)
                                    .clip(RoundedCornerShape(MaterialTheme.dimens.small)),
                                contentScale = ContentScale.Crop
                            )

                            // Image Counter Overlay at Bottom Left
                            Box(
                                modifier = Modifier
                                    .align(Alignment.BottomStart)
                                    .padding(MaterialTheme.dimens.betweenSmallMedium)
                            ) {
                                ImageCounterOverlay(imageCount = similarAd.media?.mediaCount ?: 0)
                            }
                            // Favorite Icon at Top Right
                            if (!isConnectedShop)
                                Icon(
                                    painter = painterResource(R.drawable.ic_favorite),
                                    contentDescription = "Favorite",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .padding(MaterialTheme.dimens.betweenSmallMedium)
                                        .size(18.dp)
                                )
                        }
                        Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
                        Text(
                            text = similarAd.title,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                            style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
                            textAlign = TextAlign.Start,
                            modifier = Modifier.padding(top = MaterialTheme.dimens.small)
                        )
                        val price = similarAd.price.getCurrentPriceWithCurrency()
                        if (!price.isNullOrEmpty()) {
                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.tiny))
                            Text(
                                text = price,
                                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(vertical = MaterialTheme.dimens.small)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ImageCounterOverlay(imageCount: Int) {
    Box(
        modifier = Modifier
            .background(
                Color.Black.copy(alpha = 0.4f),
                RoundedCornerShape(MaterialTheme.dimens.regular)
            )
            .padding(horizontal = 5.dp, vertical = 1.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                painter = painterResource(R.drawable.ic_photo_camera),
                contentDescription = "Camera Icon",
                tint = Color.White,
                modifier = Modifier.size(MaterialTheme.dimens.default)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
            Text(
                text = "$imageCount",
                color = Color.White,
                fontSize = 12.sp
            )
        }
    }
}

