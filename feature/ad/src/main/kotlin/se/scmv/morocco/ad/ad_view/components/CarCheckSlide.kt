package se.scmv.morocco.ad.ad_view.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import se.scmv.morocco.ad.R
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.AvitoRead
import se.scmv.morocco.designsystem.theme.Blue900
import se.scmv.morocco.designsystem.theme.Gray500
import se.scmv.morocco.designsystem.theme.White600
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.CarCheckConfig

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun CarCheckSlide(
    carCheckConfig: CarCheckConfig,
    onInspectClicked: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val lang = LocaleManager.getCurrentLanguage()
    val carToCheck = carCheckConfig.carToCheck
    val localeContent = carToCheck?.locales?.get(lang)

    Surface(
        modifier = modifier
            .fillMaxSize(),
        color = MaterialTheme.colorScheme.surface
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = MaterialTheme.dimens.extraBig + MaterialTheme.dimens.large)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = MaterialTheme.dimens.default)
                    .padding(start = MaterialTheme.dimens.default),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
            Text(
                text = localeContent?.title.toString(),
                style = MaterialTheme.typography.titleSmall,
                color = Blue900
            )
            }

            Text(
                text = localeContent?.description.toString(),
                style = MaterialTheme.typography.bodySmall,
                color = Gray500,
                modifier = Modifier.padding(
                    top = MaterialTheme.dimens.medium,
                    start = MaterialTheme.dimens.default,
                    bottom = MaterialTheme.dimens.medium
                )
            )

            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = MaterialTheme.dimens.default)
            ) {
                localeContent?.tags?.forEach { tag ->
                    Text(
                        text = tag,
                        style = MaterialTheme.typography.labelSmall,
                        color = Blue900,
                        modifier = Modifier
                            .clip(RoundedCornerShape(MaterialTheme.dimens.betweenSmallMedium))
                            .background(White600)
                            .padding(
                                horizontal = MaterialTheme.dimens.betweenSmallMedium,
                                vertical = MaterialTheme.dimens.tiny
                            )
                    )
                }
            }

            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.BottomCenter
            ) {
                Image(
                    painter = painterResource(id = R.drawable.car_wave_ic),
                    contentDescription = "Background",
                    contentScale = ContentScale.FillWidth,
                    modifier = Modifier.fillMaxWidth()
                )

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 120.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.car_check),
                            contentDescription = "Car",
                            modifier = Modifier.fillMaxWidth(0.6f)
                        )
                    }
                }

                AvPrimaryButton(
                    modifier = Modifier
                        .fillMaxWidth(0.7f)
                        .padding(bottom = MaterialTheme.dimens.regular),
                    text = localeContent?.buttonText.toString(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = AvitoRead
                    ),
                    onClick = onInspectClicked
                )
            }
        }
    }
}
