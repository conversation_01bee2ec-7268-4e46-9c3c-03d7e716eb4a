package se.scmv.morocco.ad.ad_view.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import kotlinx.datetime.LocalDateTime
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.AvButtonWithTextAndIcon
import se.scmv.morocco.designsystem.theme.AvitoColors
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray300
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.AdDetails

@Composable
fun ContactSellerSlide(
    seller: AdDetails.Seller?,
    onWhatsAppClick: () -> Unit,
    onMessageClick: () -> Unit,
    onCallClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val whatsappPainter = painterResource(R.drawable.ic_whatsapp)
    val messagePainter = painterResource(R.drawable.ic_message)
    val verifiedCheckPainter = painterResource(R.drawable.verified_check_icon)
    val calendarPainter = painterResource(R.drawable.ic_stats_calendar)

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
            .padding(
                top = MaterialTheme.dimens.extraBig,
                start = MaterialTheme.dimens.extraBig,
                end = MaterialTheme.dimens.extraBig,
                bottom = MaterialTheme.dimens.big
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {


            Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(MaterialTheme.dimens.extraExtraBig)
                        .background(
                            color = Color.Transparent,
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    AsyncImage(
                        model = seller?.getSellerLogo(),
                        contentDescription = if (seller?.isShop() == true) "Shop Logo" else "Seller Avatar",
                        modifier = Modifier
                            .size(MaterialTheme.dimens.extraExtraBig)
                            .clip(CircleShape),
                        contentScale = ContentScale.Crop
                    )

                    if (seller?.getSellerLogo().isNullOrEmpty()) {
                        if (seller?.isShop() == true) {
                            Image(
                                painter = painterResource(R.drawable.ic_shop),
                                contentDescription = "Shop",
                                modifier = Modifier
                                    .size(MaterialTheme.dimens.extraExtraBig)
                                    .clip(CircleShape)
                            )
                        } else {
                            Image(
                                painter = painterResource(R.drawable.ic_profile_placeholder),
                                contentDescription = "Profile Image",
                                modifier = Modifier
                                    .size(MaterialTheme.dimens.extraExtraBig)
                                    .clip(CircleShape)
                                    .background(Gray300)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.width(MaterialTheme.dimens.regular))

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = seller?.getNullableName().orEmpty(),
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    if (seller?.isVerified() == true) {
                        Spacer(modifier = Modifier.width(MaterialTheme.dimens.tiny))
                        Icon(
                            painter = verifiedCheckPainter,
                            contentDescription = "Verified Seller",
                            tint = Color(0xFFFFA500),
                            modifier = Modifier.size(MaterialTheme.dimens.default)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))

            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
            ) {
                if (seller?.hasPhone() == true) {
                    AvButtonWithTextAndIcon(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(MaterialTheme.dimens.extraBig),
                        icon = R.drawable.ic_call_grid,
                        buttonTitle = stringResource(R.string.show_number),
                        shape = RoundedCornerShape(MaterialTheme.dimens.medium),
                        onClick = onCallClick
                    )
                }

                if (seller?.phoneStartWith05() == true && seller?.hasPhone() == true) {
                    Button(
                        onClick = onWhatsAppClick,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(MaterialTheme.dimens.extraBig),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = AvitoColors.PriceInquiryWhatsAppBackground,
                            contentColor = AvitoColors.PriceInquiryWhatsAppForeground
                        ),
                        shape = RoundedCornerShape(MaterialTheme.dimens.medium),
                        border = BorderStroke(1.dp, AvitoColors.PriceInquiryWhatsAppForeground)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                        ) {
                            Icon(
                                painter = whatsappPainter,
                                contentDescription = "WhatsApp",
                                tint = AvitoColors.PriceInquiryWhatsAppForeground,
                                modifier = Modifier.size(MaterialTheme.dimens.big)
                            )
                            Text(
                                text = stringResource(R.string.contact_whatsapp),
                                color = AvitoColors.PriceInquiryWhatsAppForeground,
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }

                OutlinedButton(
                    onClick = onMessageClick,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(MaterialTheme.dimens.extraBig),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = AvitoColors.PriceInquiryChatForeground
                    ),
                    border = BorderStroke(
                        width = 1.dp,
                        color = AvitoColors.PriceInquiryChatForeground
                    ),
                    shape = RoundedCornerShape(MaterialTheme.dimens.medium)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                    ) {
                        Icon(
                            painter = messagePainter,
                            contentDescription = "Chat",
                            tint = AvitoColors.PriceInquiryChatForeground,
                            modifier = Modifier.size(MaterialTheme.dimens.big)
                        )
                        Text(
                            text = stringResource(R.string.chat),
                            color = AvitoColors.PriceInquiryChatForeground,
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.weight(1f))

            if (seller?.getSellerRegistrationDay() != null) {
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        painter = calendarPainter,
                        contentDescription = "Date",
                        tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                        modifier = Modifier.size(MaterialTheme.dimens.default)
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                    Text(
                        text = "${stringResource(R.string.member_since)} ${seller.getSellerRegistrationDay()?.year ?: "2018"}",
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                        fontSize = 12.sp
                    )
                }
            }

        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ContactSellerSlidePreview() {
    AvitoTheme {
        ContactSellerSlide(
            seller = AdDetails.Seller.Store(
                storeId = "123",
                name = "Boutique Fashion Store",
                phone = AdDetails.Details.Phone(
                    number = "**********",
                    verified = true
                ),
                isVerifiedSeller = true,
                logo = AdDetails.Details.Logo(
                    defaultPath = "https://example.com/logo.jpg"
                ),
                registrationDay = LocalDateTime(2020, 5, 15, 10, 30)
            ),
            onWhatsAppClick = {},
            onMessageClick = {},
            onCallClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ContactSellerSlideIndividualPreview() {
    AvitoTheme {
        ContactSellerSlide(
            seller = AdDetails.Seller.Private(
                accountId = "456",
                name = "Ahmed Benali",
                phone = AdDetails.Details.Phone(
                    number = "**********",
                    verified = false
                ),
                registrationDay = LocalDateTime(2018, 3, 10, 14, 20)
            ),
            onWhatsAppClick = {},
            onMessageClick = {},
            onCallClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ContactSellerSlidePrivateNoAvatarPreview() {
    AvitoTheme {
        ContactSellerSlide(
            seller = AdDetails.Seller.Private(
                accountId = "789",
                name = "Sarah Alami",
                phone = AdDetails.Details.Phone(
                    number = "**********",
                    verified = true
                ),
                registrationDay = LocalDateTime(2019, 8, 22, 16, 45)
            ),
            onWhatsAppClick = {},
            onMessageClick = {},
            onCallClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ContactSellerSlideNoPhonePreview() {
    AvitoTheme {
        ContactSellerSlide(
            seller = AdDetails.Seller.Store(
                storeId = "789",
                name = "Store Without Phone",
                phone = null,
                isVerifiedSeller = true,
                logo = null,
                registrationDay = LocalDateTime(2022, 1, 1, 9, 0)
            ),
            onWhatsAppClick = {},
            onMessageClick = {},
            onCallClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp)
        )
    }
}
