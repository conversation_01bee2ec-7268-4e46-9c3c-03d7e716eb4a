package se.scmv.morocco.authentication.presentation.private_account.auth

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl

class PrivateAccountSignInCredentialsTest {

    @get:Rule(order = 0)
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    private val authenticationRepository = AuthenticationRepositoryConfigurableImpl()

    @Test
    fun signIn_emailOrPhoneFiledEmpty() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = authenticationRepository
        ) {
            typeEmailOrPhone("")
            submit()
        } verify {
            emailOrPhoneRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signIn_emailFormatInvalid() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = authenticationRepository
        ) {
            listOf(
                "example.com",
                "user@@example.com",
                "user@example@com",
                "user@exam!ple.com",
                "user@ example.com",
                "  <EMAIL>",
                "<EMAIL>  ",
                "üñîçødë@example.com",
                "user@.com",
                "<EMAIL>.",
            ).forEach { email ->
                typeEmailOrPhone(email)
                submit()
                verify {
                    emailFormatInvalidErrorIsDisplayed()
                }
            }
        }
    }

    @Test
    fun signIn_emailFormatValid() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = authenticationRepository
        ) {
            listOf(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ).forEach { email ->
                typeEmailOrPhone(email)
                submit()
                verify {
                    emailHasNoError()
                }
            }
        }
    }

    @Test
    fun signIn_phoneFormatInvalid() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = authenticationRepository
        ) {
            listOf(
                "*********",
                "*********",
                "15*********",
                "**********",
            ).forEach { phone ->
                typeEmailOrPhone(phone)
                submit()
                verify {
                    phoneFormatInvalidErrorIsDisplayed()
                }
            }
        }
    }

    @Test
    fun signIn_phoneFormatValid() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = authenticationRepository
        ) {
            listOf(
                "*********8",
                "**********",
                "**********",
                "**********",
                "0*********1",
                "*************"
            ).forEach { phone ->
                typeEmailOrPhone(phone)
                submit()
                verify {
                    phoneHasNoError()
                }
            }
        }
    }

    @Test
    fun signIn_passwordFormatInvalid() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = authenticationRepository
        ) {
            listOf("", "  ").forEach { password ->
                typeEmailOrPhone("<EMAIL>")
                typePassword(password)
                submit()
                verify {
                    passwordRequiredErrorIsDisplayed()
                }
            }
        }
    }
}