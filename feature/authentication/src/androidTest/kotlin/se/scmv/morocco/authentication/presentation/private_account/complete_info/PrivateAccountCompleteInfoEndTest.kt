package se.scmv.morocco.authentication.presentation.private_account.complete_info

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource

class PrivateAccountCompleteInfoEndTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun completeInfo_backendError() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                signUpResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(
                        message = AuthenticationRepositoryConfigurableImpl.USERS_NAME_INVALID,
                    )
                )
            )
        ) {
            typeFullName("Jhon DOE")
            typePassword("Azerty@123")
            typePasswordConfirm("Azerty@123")
            submit()
        } verify {
            fullNameInvalidErrorIsDisplayed()
        }
    }

    @Test
    fun completeInfo_NetworkError() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                signUpResponse = Resource.Failure(
                    NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)
                )
            )
        ) {
            typeFullName("Jhon DOE")
            typePassword("Azerty@123")
            typePasswordConfirm("Azerty@123")
            submit()
        } verify {
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun completeInfo_success() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeFullName("Jhon DOE")
            typePassword("Azerty@123")
            typePasswordConfirm("Azerty@123")
            submit()
        } verify {
            fullNameRequiredErrorIsNotDisplayed()
            passwordRequiredErrorIsNotDisplayed()
            passwordConfirmRequiredErrorIsNotDisplayed()
            fullNameInvalidErrorIsNotDisplayed()
            networkErrorIsNotDisplayed()
        }
    }
}