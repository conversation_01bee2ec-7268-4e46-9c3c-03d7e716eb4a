package se.scmv.morocco.authentication.presentation.private_account.complete_info

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.assertIsNotEnabled
import androidx.compose.ui.test.assertIsOn
import androidx.compose.ui.test.assertTextEquals
import androidx.compose.ui.test.hasClickAction
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performScrollTo
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.lifecycle.SavedStateHandle
import androidx.test.ext.junit.rules.ActivityScenarioRule
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.authentication.presentation.navigation.ARG_OTP_CODE
import se.scmv.morocco.authentication.presentation.navigation.ARG_PHONE_NUMBER
import se.scmv.morocco.designsystem.components.TEST_TAG_TOS_AND_PP
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController

const val VERIFIED_PHONE_NUMBER = "**********"
const val VERIFICATION_CODE = "061122"

fun launchPrivateAccountCompleteInfoScreen(
    rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
    authenticationRepository: AuthenticationRepository,
    block: PrivateAccountCompleteInfoRobot.() -> Unit
): PrivateAccountCompleteInfoRobot {
    val viewModel = PrivateAccountCompleteInfoViewModel(
        savedStateHandle = SavedStateHandle().apply {
            set(ARG_PHONE_NUMBER, VERIFIED_PHONE_NUMBER)
            set(ARG_OTP_CODE, VERIFICATION_CODE)
        },
        credentialsValidator = CredentialsValidator(),
        authenticationRepository = authenticationRepository
    )
    rule.setContent {
        AvitoTheme {
            Scaffold(
                snackbarHost = { SnackBarHostForSnackBarController() }
            ) {
                PrivateAccountCompleteInfoRoute(
                    modifier = Modifier.padding(it),
                    viewModel = viewModel,
                    navigateBack = {},
                    navigateToHome = {},
                    navigateToWebViewScreen = { _, _ -> }
                )
            }
        }
    }
    return PrivateAccountCompleteInfoRobot(rule).apply(block)
}

class PrivateAccountCompleteInfoRobot(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun typeFullName(fullName: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_FULL_NAME)
            .apply {
                performTextClearance()
                performTextInput(fullName)
            }
    }

    fun typePassword(password: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_PASSWORD)
            .apply {
                performTextClearance()
                performTextInput(password)
            }
    }

    fun typePasswordConfirm(passwordConfirm: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_PASSWORD_CONFIRM)
            .apply {
                performTextClearance()
                performTextInput(passwordConfirm)
            }
    }

    /**
     * Assume that the cgu is checked by default.
     */
    fun uncheckTosAndPp() {
        rule.onNodeWithTag(TEST_TAG_TOS_AND_PP)
            .performClick()
    }

    fun submit() {
        val buttonText = rule.activity.getString(R.string.private_account_complete_info_screen_submit_button)
        rule.onNode(hasText(buttonText) and hasClickAction())
            // NB: because our form is too large and we're using verticalScroll(rememberScrollState())
            // So we must scrollTo this button to perform click, else the click will not performed
            // and tests will fail.
            .performScrollTo()
            .performClick()
    }

    infix fun verify(
        block: PrivateAccountCompleteInfoVerification.() -> Unit
    ): PrivateAccountCompleteInfoVerification {
        return PrivateAccountCompleteInfoVerification(rule).apply(block)
    }
}

class PrivateAccountCompleteInfoVerification(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun phoneNumberIsAlreadyFiledAndDisabled() {
        rule.onNodeWithTag(TestTags.TEST_TAG_PHONE_NUMBER)
            .assertIsDisplayed()
            .assertIsNotEnabled()
            .assertTextEquals(VERIFIED_PHONE_NUMBER)
    }

    fun fullNameRequiredErrorIsDisplayed() {
        val fullNameRequired = rule.activity.getString(R.string.common_full_name_field_required)
        rule.onNodeWithText(fullNameRequired)
            .assertIsDisplayed()
    }

    fun fullNameRequiredErrorIsNotDisplayed() {
        val fullNameRequired = rule.activity.getString(R.string.common_full_name_field_required)
        rule.onNodeWithText(fullNameRequired)
            .assertIsNotDisplayed()
    }

    fun passwordRequiredErrorIsDisplayed() {
        val passwordRequired = rule.activity.getString(R.string.common_password_field_required)
        rule.onNodeWithText(passwordRequired)
            .assertIsDisplayed()
    }

    fun passwordRequiredErrorIsNotDisplayed() {
        val passwordRequired = rule.activity.getString(R.string.common_password_field_required)
        rule.onNodeWithText(passwordRequired)
            .assertIsNotDisplayed()
    }

    fun tosAndPpIsAlreadyChecked() {
        rule.onNodeWithTag(TEST_TAG_TOS_AND_PP)
            .assertIsOn()
    }

    fun passwordConfirmRequiredErrorIsDisplayed() {
        val passwordConfirmRequired = rule.activity.getString(R.string.common_password_confirm_field_required)
        rule.onNodeWithText(passwordConfirmRequired)
            .assertIsDisplayed()
    }

    fun passwordConfirmRequiredErrorIsNotDisplayed() {
        val passwordConfirmRequired = rule.activity.getString(R.string.common_password_confirm_field_required)
        rule.onNodeWithText(passwordConfirmRequired)
            .assertIsNotDisplayed()
    }

    fun tosAndPpConfirmationRequiredErrorIsDisplayed() {
        val tosAndPpConfirmation = rule.activity.getString(R.string.common_tos_and_pp_field_required)
        rule.onNodeWithText(tosAndPpConfirmation)
            .assertIsDisplayed()
    }

    fun passwordDoNotMatchErrorIsDisplayed() {
        val passwordsDoNotMatchError = rule.activity.getString(R.string.common_password_confirm_field_not_match)
        rule.onNodeWithText(passwordsDoNotMatchError)
            .assertIsDisplayed()
    }

    fun fullNameInvalidErrorIsDisplayed() {
        rule.onNodeWithText(AuthenticationRepositoryConfigurableImpl.USERS_NAME_INVALID)
            .assertIsDisplayed()
    }

    fun fullNameInvalidErrorIsNotDisplayed() {
        rule.onNodeWithText(AuthenticationRepositoryConfigurableImpl.USERS_NAME_INVALID)
            .assertIsNotDisplayed()
    }

    fun networkErrorIsDisplayed() {
        val networkError = rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsDisplayed()
    }

    fun networkErrorIsNotDisplayed() {
        val networkError = rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsNotDisplayed()
    }
}