package se.scmv.morocco.authentication.presentation.private_account.complete_info

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.navigation.ARG_OTP_CODE
import se.scmv.morocco.authentication.presentation.navigation.ARG_PHONE_NUMBER
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject

@HiltViewModel
class PrivateAccountCompleteInfoViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val credentialsValidator: CredentialsValidator,
    private val authenticationRepository: AuthenticationRepository,
) : ViewModel() {

    private val phoneNumber: String = requireNotNull(savedStateHandle[ARG_PHONE_NUMBER]) {
        "Phone number cannot be null! make sure you passed a phone number in navigation."
    }

    private val otpCode: String? = savedStateHandle.get<String>(ARG_OTP_CODE)

    private val _viewState = MutableStateFlow(PrivateAccountCompleteInfoViewState(phoneNumber))
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<Unit>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onFullNameChanged(fullName: String) {
        _viewState.update { it.copy(fullName = fullName, fullNameError = null) }
    }

    fun onPasswordChanged(password: String) {
        _viewState.update { it.copy(password = password, passwordError = null) }
    }

    fun onPasswordConfirmChanged(passwordConfirm: String) {
        _viewState.update {
            it.copy(passwordConfirm = passwordConfirm, passwordConfirmError = null)
        }
    }

    fun onTosAndPpConfirmChanged(checked: Boolean) {
        _viewState.update { it.copy(confirmTosAndPp = checked, confirmTosAndPpError = null) }
    }

    fun onSubmit() {
        if (validateForm().not()) return

        val (phoneNumber, fullName, password) = _viewState.value
        showLoading()
        viewModelScope.launch {
            val result = authenticationRepository.registerAccount(
                phoneNumber = phoneNumber,
                fullName = fullName,
                otpCode = otpCode,
                password = password,
            )
            hideLoading()
            updateFor(result)
        }
    }

    private fun validateForm(): Boolean {
        val (_, fullName, password, passwordConfirm, tosAndPpConfirm) = _viewState.value

        if (credentialsValidator.validateFullName(fullName).not()) {
            _viewState.update {
                it.copy(fullNameError = UiText.FromRes(R.string.common_full_name_field_required))
            }
            return false
        }

        if (password.isBlank()) {
            _viewState.update {
                it.copy(passwordError = UiText.FromRes(R.string.common_password_field_required))
            }
            return false
        }
        if (passwordConfirm.isBlank()) {
            _viewState.update {
                it.copy(passwordConfirmError = UiText.FromRes(R.string.common_password_confirm_field_required))
            }
            return false
        }
        if (password != passwordConfirm) {
            _viewState.update {
                it.copy(passwordConfirmError = UiText.FromRes(R.string.common_password_confirm_field_not_match))
            }
            return false
        }
        if (tosAndPpConfirm.not()) {
            _viewState.update {
                it.copy(confirmTosAndPpError = R.string.common_tos_and_pp_field_required)
            }
            return false
        }
        return true
    }

    private suspend fun updateFor(result: Resource<Unit, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> _oneTimeEvents.emit(Unit)

            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }
}