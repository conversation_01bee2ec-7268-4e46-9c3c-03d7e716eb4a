package se.scmv.morocco.authentication.presentation.private_account.auth

import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.LocalOverscrollConfiguration
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.google.android.gms.auth.api.identity.Identity
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.GoogleAuthUi
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPasswordField
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray500
import se.scmv.morocco.designsystem.theme.dimens
import java.util.Random

@Composable
fun PrivateAccountAuthRoute(
    navigateBack: () -> Unit,
    navigateToResetPassword: () -> Unit,
    navigateToOtpValidation: (String) -> Unit,
    navigateToStoreSignIn: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: PrivateAccountAuthViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val googleAuthUi = remember { GoogleAuthUi(context, Identity.getSignInClient(context)) }
    val googleSignInLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { result ->
        result.data?.let {
            val token = googleAuthUi.getGoogleIdToken(it) ?: return@let
            viewModel.onSignInWithGoogle(token)
        }
    }

    val state = viewModel.viewState.collectAsState().value
    var googleLoading by remember { mutableStateOf(false) }
    Column(modifier = modifier.fillMaxSize()) {
        AvTopAppBar(onNavigationIconClicked = navigateBack)
        PrivateAccountAuthScreen(
            modifier = modifier.fillMaxSize()
                .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
                .padding(top = MaterialTheme.dimens.medium)
                .padding(bottom = MaterialTheme.dimens.screenPaddingHorizontal),
            state = state,
            onPhoneNumberChanged = viewModel::onPhoneNumberChanged,
            onCountrySelected = viewModel::onCountrySelected,
            onPasswordChanged = viewModel::onPasswordChanged,
            onPasswordForgottenClicked = navigateToResetPassword,
            onCheckAccountClicked = viewModel::onCheckAccount,
            onSubmitBtnClicked = viewModel::onSubmit,
            onGoogleBtnClicked = {
                scope.launch {
                    googleLoading = true
                    val signInIntent = googleAuthUi.signIn()
                    googleLoading = false
                    if (signInIntent == null) {
                        Toast.makeText(
                            context,
                            R.string.common_unexpected_error_verify_and_try_later,
                            Toast.LENGTH_SHORT
                        ).show()
                        return@launch
                    }
                    googleSignInLauncher.launch(IntentSenderRequest.Builder(signInIntent).build())
                }
            },
            onGoToStoreSignInBtnClicked = navigateToStoreSignIn
        )
    }
    
    if (googleLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
    
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collectLatest { event ->
            when (event) {
                is PrivateAccountMergedOneTimeEvents.Success -> navigateBack()
                is PrivateAccountMergedOneTimeEvents.Otp -> {
                    navigateToOtpValidation(event.phone)
                }
            }
        }
    }
    
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.LOGIN,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE
            )
        )
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun PrivateAccountAuthScreen(
    modifier: Modifier = Modifier,
    state: PrivateAccountMergedViewState,
    onPhoneNumberChanged: (String) -> Unit,
    onCountrySelected: (Country) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onCheckAccountClicked: () -> Unit,
    onSubmitBtnClicked: () -> Unit,
    onGoogleBtnClicked: () -> Unit,
    onGoToStoreSignInBtnClicked: () -> Unit
) {
    CompositionLocalProvider(
        LocalOverscrollConfiguration provides null
    ) {
        Column(
            modifier = modifier.verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
        ) {
            PrivateAccountAuthForm(
                viewState = state,
                onPhoneNumberChanged = onPhoneNumberChanged,
                onCountrySelected = onCountrySelected,
                onPasswordChanged = onPasswordChanged,
                onPasswordForgottenClicked = onPasswordForgottenClicked,
                onCheckAccountClicked = onCheckAccountClicked,
                onSubmit = onSubmitBtnClicked,
            )
            
            // Always show Google sign-in, not just when account exists
            PrivateAccountAuthSocialMediaProviders(
                onGoogleBtnClicked = onGoogleBtnClicked
            )
            
            // Shop sign-in button
            Button(
                modifier = Modifier.fillMaxWidth(),
                onClick = onGoToStoreSignInBtnClicked,
                shape = MaterialTheme.shapes.small,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.surfaceContainer,
                    contentColor = MaterialTheme.colorScheme.onSurface
                )
            ) {
                Text(
                    text = stringResource(id = R.string.private_account_merged_screen_shop_signin),
                    style = MaterialTheme.typography.titleSmall
                )
            }
        }
    }
}

@Composable
private fun PrivateAccountAuthForm(
    viewState: PrivateAccountMergedViewState,
    onPhoneNumberChanged: (String) -> Unit,
    onCountrySelected: (Country) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onCheckAccountClicked: () -> Unit,
    onSubmit: () -> Unit,
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraBig)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvScreenTitle(title = R.string.private_account_merged_screen_title)
            AvScreenSubTitle(title = R.string.private_account_merged_screen_subtitle)
        }
        
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            // Phone number field with country code selector
            PhoneNumberFieldWithCountryCode(
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag(TestTags.TEST_TAG_PHONE_NUMBER),
                phoneNumber = viewState.phoneNumber,
                selectedCountry = viewState.selectedCountry,
                onPhoneNumberChanged = onPhoneNumberChanged,
                onCountrySelected = onCountrySelected,
                error = viewState.phoneNumberError?.getValue(context),
                onNext = {
                    if (viewState.accountExists == null) {
                        onCheckAccountClicked()
                    }
                }
            )
            
            if (viewState.showPasswordField) {
                AvPasswordField(
                    modifier = Modifier
                        .fillMaxWidth()
                        .testTag(TestTags.TEST_TAG_PASSWORD),
                    value = viewState.password,
                    onValueChanged = onPasswordChanged,
                    title = R.string.common_password_field_label,
                    placeholder = R.string.common_password_field_placeholder,
                    error = viewState.passwordError?.getValue(context),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                            onSubmit()
                        }
                    )
                )
                
                if (viewState.showForgotPassword) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(MaterialTheme.dimens.extraBig),
                        verticalArrangement = Arrangement.Top,
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(
                            modifier = Modifier.clickable(onClick = onPasswordForgottenClicked),
                            text = stringResource(id = R.string.private_account_sign_in_screen_password_forgotten),
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            // Action button
            when {
                viewState.checkingAccount -> {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                viewState.accountExists == true -> {
                    AvPrimaryButton(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.private_account_sign_in_screen_submit_button),
                        loading = viewState.loading,
                        onClick = {
                            focusManager.clearFocus()
                            onSubmit()
                        }
                    )
                }
                else -> {
                    AvPrimaryButton(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.common_continue),
                        loading = viewState.loading,
                        onClick = {
                            focusManager.clearFocus()
                            onCheckAccountClicked()
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun PrivateAccountAuthSocialMediaProviders(
    onGoogleBtnClicked: () -> Unit,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier.padding(vertical = MaterialTheme.dimens.large),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
        ) {
            HorizontalDivider(modifier = Modifier.weight(1f))
            Text(
                text = stringResource(id = R.string.private_account_sign_in_screen_other_providers),
                color = Gray500,
                style = MaterialTheme.typography.bodySmall
            )
            HorizontalDivider(modifier = Modifier.weight(1f))
        }

        OutlinedButton(
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline),
            onClick = {
                onGoogleBtnClicked()
            },
            shape = MaterialTheme.shapes.small
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_google),
                contentDescription = stringResource(id = R.string.private_account_sign_in_screen_other_providers),
                modifier = Modifier.size(MaterialTheme.dimens.big)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
            Text(text = stringResource(R.string.private_account_sign_in_screen_login_with_google))
        }
    }
}

@Composable
private fun PhoneNumberFieldWithCountryCode(
    modifier: Modifier = Modifier,
    phoneNumber: String,
    selectedCountry: Country?,
    onPhoneNumberChanged: (String) -> Unit,
    onCountrySelected: (Country) -> Unit,
    error: String? = null,
    onNext: () -> Unit = {}
) {
    OutlinedTextField(
        value = phoneNumber,
        onValueChange = { input ->
            if (input.all { it.isDigit() }) {
                if (input.length <= 12) {
                    onPhoneNumberChanged(input)
                }
            } else if (input.isEmpty()) {
                onPhoneNumberChanged(input)
            }
        },
        label = { Text(stringResource(R.string.common_phone_number_field_label)) },
        placeholder = { Text(stringResource(R.string.common_phone_number_field_placeholder)) },
        leadingIcon = {
            CountryField(
                modifier = Modifier.padding(start = MaterialTheme.dimens.medium),
                country = selectedCountry,
                onCountrySelected = onCountrySelected
            )
        },
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Phone,
            imeAction = ImeAction.Next
        ),
        keyboardActions = KeyboardActions(
            onNext = { onNext() }
        ),
        modifier = modifier,
        supportingText = error?.let {
            {
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        },
        isError = error != null
    )
}

@Composable
private fun CountryField(
    modifier: Modifier = Modifier,
    country: Country? = null,
    onCountrySelected: (Country) -> Unit
) {
    val context = LocalContext.current

    val countriesList = remember {
        context.resources.getStringArray(R.array.country_list)
            .mapNotNull { item ->
                val parts = item.split("|")
                if (parts.size == 3) {
                    val flag = parts[0].trim()
                    val displayName = parts[1].trim()
                    val phonePrefix = parts[2].trim()
                    Country(
                        flag = flag,
                        displayName = displayName,
                        phonePrefix = phonePrefix
                    )
                } else {
                    null
                }
            }
    }

    var expanded by remember { mutableStateOf(false) }

    var selectedCountry by remember { mutableStateOf(country) }
    
    // Update selectedCountry when country parameter changes
    LaunchedEffect(country) {
        selectedCountry = country
    }

    Box(modifier = modifier.wrapContentSize()) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .clickable { expanded = true }
                .padding(MaterialTheme.dimens.medium)
        ) {
            Text(
                text = selectedCountry?.phonePrefix.orEmpty(),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
            Icon(
                imageVector = Icons.Default.ArrowDropDown,
                contentDescription = null
            )
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            countriesList.forEach { countryItem ->
                DropdownMenuItem(
                    onClick = {
                        selectedCountry = countryItem
                        onCountrySelected(countryItem)
                        expanded = false
                    },
                    text = {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(
                                text = countryItem.phonePrefix, 
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                            Text(text = countryItem.flag, fontSize = 16.sp)
                            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                            Text(text = countryItem.displayName, fontSize = 14.sp)
                        }
                    }
                )
            }
        }
    }
}

@Preview
@Composable
private fun PrivateAccountAuthScreenPreview() {
    AvitoTheme {
        Surface {
            var state by remember {
                mutableStateOf(PrivateAccountMergedViewState())
            }
            PrivateAccountAuthScreen(
                state = state,
                onPhoneNumberChanged = {
                    state = state.copy(phoneNumber = it)
                },
                onCountrySelected = { country ->
                    state = state.copy(selectedCountry = country)
                },
                onPasswordChanged = {
                    state = state.copy(password = it)
                },
                onPasswordForgottenClicked = {},
                onCheckAccountClicked = {},
                onSubmitBtnClicked = {},
                onGoogleBtnClicked = {},
                onGoToStoreSignInBtnClicked = {}
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DebugTopBar(
    onCredentialsSelected: (Pair<String, String>) -> Unit
) {
    val logins = listOf(
        "<EMAIL>" to "123456",
        "<EMAIL>" to "123456",
        "<EMAIL>" to "**********",
        "<EMAIL>" to "Avito1234",
        "<EMAIL>" to "<EMAIL>",
        "<EMAIL>" to "kainosisnew",
        "<EMAIL>" to "**********",
        "<EMAIL>" to "*********",
        "<EMAIL>" to "<EMAIL>",
    )

    TopAppBar(
        title = { },
        actions = {
            IconButton(
                onClick = {
                    val generator = Random()
                    val nextInt = generator.nextInt(logins.size)
                    onCredentialsSelected(logins[nextInt])
                }
            ) {
                Icon(imageVector = Icons.Default.Add, contentDescription = null)
            }
        }
    )
}