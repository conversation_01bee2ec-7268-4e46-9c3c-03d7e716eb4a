package se.scmv.morocco.authentication.presentation.private_account.complete_info

import androidx.compose.runtime.Stable
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class PrivateAccountCompleteInfoViewState(
    val phoneNumber: String,
    val fullName: String = "",
    val password: String = "",
    val passwordConfirm: String = "",
    val confirmTosAndPp: Boolean = true,
    val fullNameError: UiText? = null,
    val passwordError: UiText? = null,
    val passwordConfirmError: UiText? = null,
    val confirmTosAndPpError: Int? = null,
    val loading: Boolean = false
)