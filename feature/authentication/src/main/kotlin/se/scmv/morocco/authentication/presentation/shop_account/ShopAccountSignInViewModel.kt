package se.scmv.morocco.authentication.presentation.shop_account

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SignInResult
import se.scmv.morocco.domain.models.SignInType
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject

@HiltViewModel
class ShopAccountSignInViewModel @Inject constructor(
    private val credentialsValidator: CredentialsValidator,
    private val authenticationRepository: AuthenticationRepository
) : ViewModel() {

    private val _viewState = MutableStateFlow(ShopAccountSignInViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<ShopAccountSignInEvent>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onEmailChanged(email: String) {
        _viewState.update { it.copy(email = email, emailError = null) }
    }

    fun onPasswordChanged(password: String) {
        _viewState.update { it.copy(password = password, passwordError = null) }
    }

    fun onSubmit() {
        clearError()
        if (validateForm().not()) return

        val (email, _, password) = _viewState.value
        viewModelScope.launch {
            showLoading()
            val result = authenticationRepository.signIn(
                emailOrPhone = email,
                password = password,
                type = SignInType.EMAIL
            )
            hideLoading()
            updateForResult(result)
        }
    }

    private fun validateForm(): Boolean {
        val (email, _, password) = _viewState.value

        when {
            email.isBlank() -> R.string.common_email_field_required
            credentialsValidator.validateEmail(email)
                .not() -> R.string.common_email_field_format_invalid

            else -> null
        }?.let { emailError ->
            _viewState.update { it.copy(emailError = UiText.FromRes(emailError)) }
            return false
        }

        if (credentialsValidator.validatePassword(password).not()) {
            _viewState.update { it.copy(passwordError = UiText.FromRes(R.string.common_password_field_required)) }
            return false
        }

        return true
    }

    private suspend fun updateForResult(result: Resource<SignInResult, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> {
                when (result.data) {
                    SignInResult.SUCCESS -> _oneTimeEvents.emit(ShopAccountSignInEvent.Success)

                    SignInResult.ECOMMERCE_RESTRICTION -> renderFailure(
                        UiText.FromRes(R.string.ecommerce_store_owner_restriction_message)
                    )
                }
            }

            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    private fun clearError() {
        _viewState.update { it.copy(emailError = null, passwordError = null) }
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }
}