package se.scmv.morocco.authentication.presentation.shop_account

import androidx.compose.runtime.Stable
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class ShopAccountSignInViewState(
    val email: String = "",
    val emailError: UiText? = null,
    val password: String = "",
    val passwordError: UiText? = null,
    val loading: Boolean = false
)

sealed interface ShopAccountSignInEvent {
    data object Success: ShopAccountSignInEvent
    
    data object EcommerceRestriction : ShopAccountSignInEvent
}