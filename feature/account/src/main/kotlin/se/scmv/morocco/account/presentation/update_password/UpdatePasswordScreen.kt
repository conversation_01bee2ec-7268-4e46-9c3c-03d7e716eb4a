package se.scmv.morocco.account.presentation.update_password

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.account.R
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.designsystem.components.AvPasswordField
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.Account

@Composable
fun UpdatePasswordRoute(
    account: Account.Connected,
    viewModel: UpdatePasswordViewModel = hiltViewModel(),
    onFinished: () -> Unit,
    navigateToAuthentication: () -> Unit
) {
    val state = viewModel.viewState.collectAsStateWithLifecycle().value
    Scaffold(
        topBar = {
            AvTopAppBar(onNavigationIconClicked = onFinished)
        }
    ) {
        UpdatePasswordScreen(
            modifier = Modifier
                .fillMaxSize()
                .padding(it),
            state = state,
            onOldPasswordChanged = viewModel::onOldPasswordChanged,
            onNewPasswordChanged = viewModel::onNewPasswordChanged,
            onSubmit = viewModel::onSubmit
        )
    }
    val context = LocalContext.current
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest {
            when (it) {
                UpdatePasswordOneTimeEvents.DismissDialog -> onFinished()
                is UpdatePasswordOneTimeEvents.ShowSnackBar -> Toast.makeText(
                    context,
                    it.message.getValue(context),
                    Toast.LENGTH_SHORT
                ).show()

                UpdatePasswordOneTimeEvents.NavigateToLogin -> navigateToAuthentication()
            }
        }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.UPDATE_PASSWORD,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = account.analyticsAccountType()
            )
        )
    )
}

@Composable
private fun UpdatePasswordScreen(
    modifier: Modifier = Modifier,
    state: UpdatePasswordViewState,
    onOldPasswordChanged: (String) -> Unit,
    onNewPasswordChanged: (String) -> Unit,
    onSubmit: () -> Unit
) {
    val context = LocalContext.current
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.background, MaterialTheme.shapes.large)
            .padding(
                horizontal = MaterialTheme.dimens.big,
            )
            .padding(top = MaterialTheme.dimens.extraExtraBig)
            .padding(bottom = MaterialTheme.dimens.big),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        AvScreenTitle(title = R.string.password_reset_screen_title)
        Spacer(Modifier.height(MaterialTheme.dimens.medium))
        AvScreenSubTitle(title = R.string.password_reset_screen_subtitle)
        Spacer(Modifier.height(MaterialTheme.dimens.big))
        Image(
            modifier = Modifier.size(140.dp),
            painter = painterResource(R.drawable.img_password_change_illustration),
            contentDescription = null
        )
        Spacer(Modifier.height(MaterialTheme.dimens.big))
        AvPasswordField(
            modifier = Modifier.fillMaxWidth(),
            value = state.oldPassword,
            onValueChanged = onOldPasswordChanged,
            title = R.string.password_reset_screen_old_password_label,
            placeholder = R.string.password_reset_screen_old_password_label,
            error = state.oldPasswordError?.getValue(context),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
        )
        Spacer(Modifier.height(MaterialTheme.dimens.large))
        AvPasswordField(
            modifier = Modifier.fillMaxWidth(),
            value = state.newPassword,
            onValueChanged = onNewPasswordChanged,
            title = R.string.password_reset_screen_new_password_label,
            placeholder = R.string.password_reset_screen_new_password_label,
            error = state.newPasswordError?.getValue(context),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done)
        )
        Spacer(Modifier.height(MaterialTheme.dimens.large))
        val focusManager = LocalFocusManager.current
        AvPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.common_continue),
            loading = state.loading,
            onClick = {
                focusManager.clearFocus()
                onSubmit()
            }
        )
    }
}

@Preview
@Composable
private fun UpdatePasswordScreenPreview() {
    AvitoTheme {
        Surface {
            UpdatePasswordScreen(
                state = UpdatePasswordViewState(
                    oldPassword = "123456",
                    newPassword = "123456",
                ),
                onOldPasswordChanged = {},
                onNewPasswordChanged = {},
                onSubmit = {}
            )
        }
    }
}