package se.scmv.morocco.account.presentation.messaging

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import se.scmv.morocco.domain.repositories.ChatRepository
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MessagingSubscriptionManager @Inject constructor(
    private val chatRepository: ChatRepository
) {

    private val _unreadCount = MutableStateFlow(0)
    val unreadCount: StateFlow<Int> = _unreadCount.asStateFlow()

    private val _conversationListRefreshTrigger = MutableSharedFlow<Unit>(extraBufferCapacity = 64)
    val conversationListRefreshTrigger: SharedFlow<Unit> = _conversationListRefreshTrigger.asSharedFlow()

    private var subscriptionJob: Job? = null

    fun startSubscription() {
        if (subscriptionJob != null) return
        subscriptionJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                chatRepository.subscribeToChat()
                    .collect { event ->
                        if (!event.message.isMine) {
                            _unreadCount.value = _unreadCount.value + 1
                            // Trigger conversation list refresh when new message is received
                            _conversationListRefreshTrigger.tryEmit(Unit)
                        }
                    }
            } catch (e: Exception) {
                // Handle subscription error silently
            }
        }
    }

    fun resetUnreadCount() {
        _unreadCount.value = 0
    }

    fun stopSubscription() {
        subscriptionJob?.cancel()
        subscriptionJob = null
    }

    suspend fun initializeUnreadCount() {
        try {
            // Use the optimized unread count query for better performance
            chatRepository.getUnreadCount()
                .firstOrNull()
                ?.fold(
                    onSuccess = { unreadCount ->
                        _unreadCount.value = unreadCount
                    },
                    onFailure = { error ->
                        // Handle error silently
                    }
                )
        } catch (e: Exception) {
            // Handle exception silently
        }
    }
} 