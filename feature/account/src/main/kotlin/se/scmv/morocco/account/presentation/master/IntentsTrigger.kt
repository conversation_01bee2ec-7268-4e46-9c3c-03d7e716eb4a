package se.scmv.morocco.account.presentation.master

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.core.net.toUri
import dagger.hilt.android.qualifiers.ApplicationContext
import se.scmv.morocco.ui.R
import javax.inject.Inject

interface AppConfig {
    val versionName: String
    val versionCode: Int
}

class IntentsTrigger @Inject constructor(
    @ApplicationContext private val context: Context,
    private val appConfig: AppConfig
) {
    fun contactSupportThroughEmail(
        data: ContactSupportData,
        onError: () -> Unit
    ) {
        try {
            val email = data.email
            val subject = Uri.encode(data.subject)
            val versionName = "${appConfig.versionName} (${appConfig.versionCode})"
            val body = Uri.encode(createEmailTemplate(data = data, versionName = versionName))
            val mailtoUri = "mailto:$email?subject=$subject&body=$body"
            val intent = Intent(Intent.ACTION_VIEW).apply {
                this.data = mailtoUri.toUri()
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            onError()
        }
    }

    fun contactSupportThroughWhatsapp(
        phone: String,
        onError: () -> Unit
    ) {
        if (isWhatsappInstalled(context)) {
            Intent(
                Intent.ACTION_VIEW,
                "https://api.whatsapp.com/send?phone=${phone}".toUri()
            ).apply { addFlags(Intent.FLAG_ACTIVITY_NEW_TASK) }
                .let { context.startActivity(it) }
        } else {
            onError()
        }
    }

    private fun createEmailTemplate(data: ContactSupportData, versionName: String): String {
        return buildString {
            append(("\n\n\n\n"))
            append("----------------------\n")
            append("----------------------\n")
            append(data.footerMessage)
            append("\n")
            append(data.osVersion)
            append("Android ${Build.VERSION.RELEASE}")
            append("\n")
            append(data.deviceModel)
            append(Build.MANUFACTURER).append(" ").append(" ").append(Build.MODEL)
            append("\n")
            append(data.appVersion).append(" ").append(versionName)
            //b append(BuildConfig.VERSION_NAME)
        }
    }

    private fun isWhatsappInstalled(context: Context): Boolean {
        val pm = context.packageManager
        return isPackageInstalled("com.whatsapp", pm) || isPackageInstalled("com.whatsapp.w4b", pm)
    }

    private fun isPackageInstalled(packageName: String, packageManager: PackageManager): Boolean {
        try {
            packageManager.getPackageInfo(packageName, 0)
            return true
        } catch (_: PackageManager.NameNotFoundException) {
            return false
        }
    }
}

data class ContactSupportData(
    val email: String,
    val subject: String,
    val footerMessage: String,
    val osVersion: String,
    val deviceModel: String,
    val appVersion: String,
)

@Composable
fun createContactSupportData() = ContactSupportData(
    email = stringResource(R.string.customer_services_email),
    subject = stringResource(R.string.customer_services_email_subject),
    footerMessage = stringResource(R.string.customer_services_email_footer_message),
    osVersion = stringResource(R.string.customer_services_email_device_os_version),
    deviceModel = stringResource(R.string.customer_services_email_device_model),
    appVersion = stringResource(R.string.customer_services_email_application_version),
)