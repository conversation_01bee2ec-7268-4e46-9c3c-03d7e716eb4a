package se.scmv.morocco.account.presentation.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navDeepLink
import androidx.navigation.toRoute
import kotlinx.serialization.Serializable
import se.scmv.morocco.account.presentation.bookmarks.master.AccountBookmarksPages
import se.scmv.morocco.account.presentation.edit_account.EditAccountRoute
import se.scmv.morocco.account.presentation.messaging.ui.chat.ChatScreen
import se.scmv.morocco.account.presentation.myads.AdsRoute
import se.scmv.morocco.account.presentation.orders.AccountOrdersRoute
import se.scmv.morocco.account.presentation.update_password.UpdatePasswordRoute
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.MyAccountAdStatus
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.ui.AppDeepLinks
import se.scmv.morocco.ui.AuthProtectedContent
import se.scmv.morocco.ui.composableWithAnimation

// OFFICIAL NAVIGATION

@Serializable
data object AccountMasterRoute

@Serializable
data class AccountBookmarksRoute(val tab: AccountBookmarksPages?= null)

@Serializable
data object AccountEditRoute

@Serializable
data class AccountAdsRoute(val status: MyAccountAdStatus? = null)

@Serializable
data object AccountOrdersRoute

@Serializable
data object AccountStatisticsRoute

@Serializable
data object MessagingRoute

@Serializable
data class ChatRoute(val conversationId: String)

@Serializable
data class UpdatePasswordRoute(val email: String)

fun NavGraphBuilder.accountGraph(
    account: Account,
    navController: NavHostController,
    navigateToAuthentication: () -> Unit,
    navigateToAdInsert: (adId: String?, toImageStep: Boolean) -> Unit,
    navigateToVas: (adId: String, adCategoryKey: String, adType: String, application: VasPacksApplication) -> Unit,
    navigateToAdview: (String) -> Unit
) {
    composableWithAnimation<AccountEditRoute> {
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { connectedAccount ->
            EditAccountRoute(
                account = connectedAccount,
                navigateBack = { navController.navigateUp() },
                navigateToUpdatePassword = {
                    navController.navigate(
                        UpdatePasswordRoute(connectedAccount.connectedContact().email)
                    )
                },
                navigateToAuthentication = navigateToAuthentication,
            )
        }
    }
    composableWithAnimation<UpdatePasswordRoute> {
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { connectedAccount ->
            UpdatePasswordRoute(
                account = connectedAccount,
                onFinished = { navController.navigateUp() },
                navigateToAuthentication = navigateToAuthentication
            )
        }
    }
    composableWithAnimation<AccountAdsRoute>(
        deepLinks = listOf(navDeepLink<AccountAdsRoute>(basePath = AppDeepLinks.ACCOUNT_ADS))
    ) {
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { connectedAccount ->
            AdsRoute(
                navigateBack = { navController.navigateUp() },
                navigateToNewInsert = { adId, _, _, toImageStep ->
                    navigateToAdInsert(adId, toImageStep)
                },
                navigateToVasActivity = navigateToVas,
                navigateToAdView = { adListId, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _ ->
                    navigateToAdview(adListId)
                },
                account = connectedAccount
            )
        }
    }

    composableWithAnimation<AccountOrdersRoute>(
        deepLinks = listOf(navDeepLink<AccountAdsRoute>(basePath = AppDeepLinks.ACCOUNT_ORDERS))
    ) {
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { _ ->
            AccountOrdersRoute(
                navigateBack = { navController.navigateUp() }
            )
        }
    }
    composable<ChatRoute>(
        deepLinks = listOf(navDeepLink<ChatRoute>(basePath = AppDeepLinks.CHAT))
    ) { backStackEntry ->
        val route = backStackEntry.toRoute<ChatRoute>()
        ChatScreen(
            conversationId = route.conversationId,
            onNavigateUp = { navController.navigateUp() },
            onOpenAd = navigateToAdview
        )
    }
}
