package se.scmv.morocco.account.presentation.messaging.ui.conversation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.account.presentation.messaging.MessagingSubscriptionManager
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.Conversation
import se.scmv.morocco.domain.repositories.ChatRepository
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject


sealed interface ConversationListEvent {
    data class NavigateToChat(val conversation: Conversation) : ConversationListEvent
}

@HiltViewModel
class ConversationListViewModel @Inject constructor(
    private val chatRepository: ChatRepository,
    private val messagingSubscriptionManager: MessagingSubscriptionManager
) : ViewModel() {

    private val _events = MutableSharedFlow<ConversationListEvent>()
    val events = _events.asSharedFlow()

    // Paging3 Flow for conversations
    val conversationsPaging: Flow<PagingData<Conversation>> =
        messagingSubscriptionManager.conversationListRefreshTrigger
            .onStart { emit(Unit) }
            .flatMapLatest {
                chatRepository.getConversationsPaging()
            }
            .cachedIn(viewModelScope)


    private fun isNetworkError(error: Throwable): Boolean {
        val msg = error.message ?: ""
        return error is java.net.SocketTimeoutException ||
                error is java.net.UnknownHostException ||
                error is java.io.IOException ||
                error.javaClass.name.contains("ApolloNetworkException") ||
                msg.contains("Network error", ignoreCase = true) ||
                msg.contains("connection abort", ignoreCase = true) ||
                msg.contains("ssl", ignoreCase = true) ||
                msg.contains("i/o error", ignoreCase = true) ||
                msg.contains("null data event", ignoreCase = true)
    }


    fun onConversationClick(conversation: Conversation) {
        // Fire-and-forget mark-as-read
        viewModelScope.launch {
            try {
                chatRepository.markConversationAsRead(conversation.id)
                    .collect { result ->
                        result.fold(
                            onSuccess = { marked ->
                                if (marked) {
                                    // Reset unread count in MessagingSubscriptionManager
                                    messagingSubscriptionManager.resetUnreadCount()
                                }
                            },
                            onFailure = { error ->
                                // Silently handle mark as read failures
                            }
                        )
                    }
            } catch (e: Exception) {
            }
        }

        viewModelScope.launch {
            _events.emit(ConversationListEvent.NavigateToChat(conversation))
        }
    }

    fun onBlockUser(conversation: Conversation) {
        viewModelScope.launch {
            chatRepository.blockConversation(conversation.id)
                .collect { result ->
                    result.fold(
                        onSuccess = { blocked ->
                            if (blocked) {
                                renderSuccess(UiText.FromRes(R.string.has_been_blocked))
                                // Paging3 will automatically refresh the data
                            }
                        },
                        onFailure = { error ->
                            handleErrorExceptNetwork(error)
                            viewModelScope.launch {
                                renderFailure(UiText.Text(error.message.toString()))
                            }
                        }
                    )
                }
        }
    }

    fun onUnblockUser(conversation: Conversation) {
        viewModelScope.launch {
            chatRepository.unblockConversation(conversation.id)
                .collect { result ->
                    result.fold(
                        onSuccess = { unblocked ->
                            if (unblocked) {
                                renderSuccess(UiText.FromRes(R.string.has_been_unblocked))
                            }
                        },
                        onFailure = { error ->
                            handleErrorExceptNetwork(error)
                            viewModelScope.launch {
                                renderFailure(UiText.Text(error.message.toString()))
                            }
                        }
                    )
                }
        }
    }

    fun onDeleteChat(conversation: Conversation) {
        viewModelScope.launch {
            chatRepository.clearConversation(conversation.id)
                .collect { result ->
                    result.fold(
                        onSuccess = { cleared ->
                            if (cleared) {
                                renderSuccess(UiText.FromRes(R.string.has_been_deleted))
                                // Paging3 will automatically refresh the data
                            }
                        },
                        onFailure = { error ->
                            handleErrorExceptNetwork(error)
                            viewModelScope.launch {
                                renderFailure(UiText.Text(error.message.toString()))
                            }
                        }
                    )
                }
        }
    }

    fun onMessageCopied() {
        renderSuccess(UiText.FromRes(R.string.chat_screen_message_copied))
    }

    override fun onCleared() {
        super.onCleared()
        messagingSubscriptionManager.stopSubscription()
    }

    private fun handleErrorExceptNetwork(error: Throwable) {
        if (!isNetworkError(error)) {
            viewModelScope.launch {
                renderFailure(
                    error.message?.let { UiText.Text(it) }
                        ?: UiText.FromRes(R.string.common_unknown_error)
                )
            }
        }
    }
}

