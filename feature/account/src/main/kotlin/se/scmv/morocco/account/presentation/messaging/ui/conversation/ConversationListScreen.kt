package se.scmv.morocco.account.presentation.messaging.ui.conversation

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.Badge
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.itemKey
import coil.compose.AsyncImage
import se.scmv.morocco.account.R
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.components.AvDynamicText
import se.scmv.morocco.designsystem.components.AvTextWithEndIcon
import se.scmv.morocco.designsystem.components.IndeterminateLoading
import se.scmv.morocco.designsystem.components.ScreenEmptyState
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.theme.Red700
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.Conversation
import se.scmv.morocco.ui.getErrorAsUiText
import se.scmv.morocco.ui.isEmpty
import se.scmv.morocco.ui.isError
import se.scmv.morocco.ui.isLoading
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConversationListScreen(
    onNavigateToChat: (Conversation) -> Unit,
    onNavigateToAdsList: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: ConversationListViewModel = hiltViewModel()
) {
    val eventFlow = viewModel.events.collectAsState(initial = null)
    val conversationsPaging = viewModel.conversationsPaging.collectAsLazyPagingItems()
    var showBlockConfirmation by remember { mutableStateOf<Conversation?>(null) }
    var showClearConfirmation by remember { mutableStateOf<Conversation?>(null) }

    LaunchedEffect(eventFlow.value) {
        when (val event = eventFlow.value) {
            is ConversationListEvent.NavigateToChat -> {
                onNavigateToChat(event.conversation)
            }

            null -> {}
        }
    }

    val refreshState = rememberPullToRefreshState()
    PullToRefreshBox(
        modifier = modifier
            .fillMaxSize(),
        isRefreshing = conversationsPaging.isLoading(),
        state = refreshState,
        onRefresh = { conversationsPaging.refresh() }
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
        ) {
            when {
                conversationsPaging.isError() -> {
                    ScreenErrorState(
                        title = stringResource(R.string.conversation_list_error_state_title),
                        description = conversationsPaging.getErrorAsUiText()
                            .getValue(LocalContext.current),
                        actionText = stringResource(R.string.conversation_list_error_state_action),
                        onActionClicked = {
                            conversationsPaging.retry()
                        }
                    )
                }

                conversationsPaging.isEmpty() && conversationsPaging.isLoading() -> {
                    // Only show loading when there are no conversations yet (initial load)
                    IndeterminateLoading()
                }

                conversationsPaging.isEmpty() -> {
                    ScreenEmptyState(
                        title = R.string.conversation_list_empty_state_title,
                        description = R.string.conversation_list_empty_state_description,
                        actionText = R.string.conversation_list_empty_state_action,
                        onActionClicked = {
                            onNavigateToAdsList()
                        }
                    )
                }

                else -> {
                    // Always show conversations, even during refresh
                    ConversationList(
                        conversationsPaging = conversationsPaging,
                        onConversationClick = viewModel::onConversationClick,
                        onBlockUser = { conversation -> showBlockConfirmation = conversation },
                        onDeleteChat = { conversation -> showClearConfirmation = conversation },
                        onMessageCopied = { message ->
                            viewModel.onMessageCopied()
                        }
                    )
                }
            }
        }
    }

    // Block/Unblock confirmation dialog
    showBlockConfirmation?.let { conversation ->
        AvConfirmationAlertDialog(
            title = if (conversation.isBlockedByMe) stringResource(R.string.conversation_list_unblock_conversation_title) else stringResource(
                R.string.conversation_list_block_conversation_title
            ),
            description = if (conversation.isBlockedByMe) stringResource(R.string.conversation_list_unblock_conversation_description) else stringResource(
                R.string.conversation_list_block_conversation_description
            ),
            onConfirm = {
                if (conversation.isBlockedByMe) {
                    viewModel.onUnblockUser(conversation)
                } else {
                    viewModel.onBlockUser(conversation)
                }
                showBlockConfirmation = null
            },
            onCancel = { showBlockConfirmation = null }
        )
    }

    // Clear chat confirmation dialog
    showClearConfirmation?.let { conversation ->
        AvConfirmationAlertDialog(
            title = stringResource(R.string.chat_screen_clear_chat_title),
            description = stringResource(R.string.chat_screen_clear_chat_description),
            onConfirm = {
                viewModel.onDeleteChat(conversation)
                showClearConfirmation = null
            },
            onCancel = { showClearConfirmation = null }
        )
    }
}

@Composable
private fun ConversationList(
    conversationsPaging: LazyPagingItems<Conversation>,
    onConversationClick: (Conversation) -> Unit,
    onBlockUser: (Conversation) -> Unit,
    onDeleteChat: (Conversation) -> Unit,
    onMessageCopied: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val listState = rememberLazyListState()

    LazyColumn(
        state = listState,
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(
            horizontal = MaterialTheme.dimens.tiny,
            vertical = MaterialTheme.dimens.tiny
        ),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
    ) {

        items(
            count = conversationsPaging.itemCount,
            key = conversationsPaging.itemKey { it.id }
        ) { index ->
            val conversation = conversationsPaging[index]
            if (conversation != null) {
                ConversationItem(
                    conversation = conversation,
                    onClick = { onConversationClick(conversation) },
                    onBlockUser = { onBlockUser(conversation) },
                    onDeleteChat = { onDeleteChat(conversation) },
                    onMessageCopied = onMessageCopied
                )
            }
        }

        // Loading indicator for append state
        if (conversationsPaging.loadState.append is LoadState.Loading) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.dimens.default)
                ) {
                    IndeterminateLoading()
                }
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun ConversationItem(
    conversation: Conversation,
    onClick: () -> Unit,
    onBlockUser: () -> Unit,
    onDeleteChat: () -> Unit,
    onMessageCopied: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var showMenu by remember { mutableStateOf(false) }
    val clipboardManager = LocalClipboardManager.current
    val interactionSource = remember { MutableInteractionSource() }
    val appLocale = remember {
        val lang = LocaleManager.getCurrentLanguage()
        when (lang) {
            "ar" -> Locale("ar", "MA")
            "fr" -> Locale("fr", "MA")
            else -> Locale("fr", "MA")
        }
    }
    val timeFormat = remember(appLocale) {
        SimpleDateFormat("HH:mm", appLocale)
    }
    val dateTimeFormat = remember(appLocale) {
        SimpleDateFormat("dd MMM", appLocale)
    }
    val fullDateFormat = remember(appLocale) {
        SimpleDateFormat("dd MMM yyyy", appLocale)
    }

    val formattedDate = remember(conversation.lastMessage?.time) {
        conversation.lastMessage?.time?.let { messageTime ->
            val now = Calendar.getInstance()
            val msgTime = Calendar.getInstance().apply { time = messageTime }

            when {
                // Same day
                now.get(Calendar.YEAR) == msgTime.get(Calendar.YEAR) &&
                        now.get(Calendar.DAY_OF_YEAR) == msgTime.get(Calendar.DAY_OF_YEAR) -> {
                    timeFormat.format(messageTime)
                }
                // Same year
                now.get(Calendar.YEAR) == msgTime.get(Calendar.YEAR) -> {
                    dateTimeFormat.format(messageTime)
                }
                // Different year
                else -> {
                    fullDateFormat.format(messageTime)
                }
            }
        } ?: ""
    }

    // Card style with elevation and rounded corners
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = MaterialTheme.dimens.none, horizontal = MaterialTheme.dimens.none)
            .combinedClickable(
                interactionSource = interactionSource,
                indication = null,
                onClick = onClick,
                onLongClick = {
                    conversation.lastMessage?.let { message ->
                        clipboardManager.setText(AnnotatedString(message.text))
                        onMessageCopied(message.text)
                    }
                }
            ),
        color = MaterialTheme.colorScheme.surface,
        tonalElevation = MaterialTheme.dimens.none,
        shape = MaterialTheme.shapes.medium
    ) {
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = MaterialTheme.dimens.medium,
                        vertical = MaterialTheme.dimens.small
                    ),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Ad image or fallback with badge outside top right
                Box(
                    modifier = Modifier
                        .wrapContentSize()
                ) {
                    Box(
                        modifier = Modifier
                            .size(MaterialTheme.dimens.extraBig + MaterialTheme.dimens.bigger)
                            .clip(MaterialTheme.shapes.medium)
                            .background(MaterialTheme.colorScheme.surfaceContainer)
                    ) {
                        val adImage = conversation.ad?.media?.defaultImage?.paths?.smallThumbnail
                        if (!adImage.isNullOrBlank()) {
                            AsyncImage(
                                model = adImage,
                                contentDescription = "Ad image",
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Crop
                            )
                        } else {
                            // Fallback: initials from partner name
                            val initials = conversation.partner?.name?.split(" ")
                                ?.mapNotNull { it.firstOrNull()?.toString() }?.take(2)
                                ?.joinToString("") ?: "?"
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = initials,
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                    // Unread badge OUTSIDE top right
                    if (conversation.unreadCount > 0) {
                        Box(
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .offset(
                                    x = MaterialTheme.dimens.betweenSmallMedium,
                                    y = (-MaterialTheme.dimens.small)
                                )
                        ) {
                            Badge(
                                containerColor = Red700
                            ) {
                                Text(
                                    text = conversation.unreadCount.toString(),
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onPrimary
                                )
                            }
                        }
                    }
                }

                // Content section
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = MaterialTheme.dimens.small),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.tiny)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = MaterialTheme.dimens.none),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {


                        // Title (LTR text like French)
                        CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
                            AvDynamicText(
                                text = conversation.ad?.title
                                    ?: stringResource(id = R.string.common_ad_not_available),
                                color = MaterialTheme.colorScheme.onSurface,
                                style = if (conversation.unreadCount > 0)
                                    MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold)
                                else
                                    MaterialTheme.typography.bodyLarge,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(end = MaterialTheme.dimens.small)
                            )
                        }
                        Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium)) // spacing between date and title
                        CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Rtl) {
                            Text(
                                text = formattedDate,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier
                                    .wrapContentWidth()
                                    .padding(start = MaterialTheme.dimens.small)
                            )
                        }


                    }

                    // Partner name below title
                    conversation.partner?.name?.let { partnerName ->
                        AvDynamicText(
                            text = partnerName,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            style = MaterialTheme.typography.bodyMedium,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                    // Last message row
                    conversation.lastMessage?.let { message ->
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            when {
                                message.attachment?.type?.startsWith("image/") == true -> {
                                    androidx.compose.runtime.CompositionLocalProvider(
                                        androidx.compose.material3.LocalTextStyle provides MaterialTheme.typography.bodySmall
                                    ) {
                                        AvTextWithEndIcon(
                                            text = stringResource(R.string.conversation_list_image),
                                            iconRes = R.drawable.ic_image,
                                            textColor = if (conversation.unreadCount > 0) {
                                                MaterialTheme.colorScheme.primary
                                            } else {
                                                MaterialTheme.colorScheme.onSurfaceVariant
                                            },
                                            iconTint = if (conversation.unreadCount > 0) {
                                                MaterialTheme.colorScheme.primary
                                            } else {
                                                MaterialTheme.colorScheme.onSurfaceVariant
                                            }
                                        )
                                    }
                                }

                                message.attachment != null -> {
                                    androidx.compose.runtime.CompositionLocalProvider(
                                        androidx.compose.material3.LocalTextStyle provides MaterialTheme.typography.bodySmall
                                    ) {
                                        AvTextWithEndIcon(
                                            text = stringResource(R.string.conversation_list_file),
                                            iconRes = R.drawable.ic_attachment,
                                            textColor = if (conversation.unreadCount > 0) {
                                                MaterialTheme.colorScheme.primary
                                            } else {
                                                MaterialTheme.colorScheme.onSurfaceVariant
                                            },
                                            iconTint = if (conversation.unreadCount > 0) {
                                                MaterialTheme.colorScheme.primary
                                            } else {
                                                MaterialTheme.colorScheme.onSurfaceVariant
                                            }
                                        )
                                    }
                                }

                                else -> {
                                    Text(
                                        text = message.text,
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                        style = if (conversation.unreadCount > 0) MaterialTheme.typography.bodySmall.copy(
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                                        ) else MaterialTheme.typography.bodySmall,
                                        color = if (conversation.unreadCount > 0) {
                                            MaterialTheme.colorScheme.primary
                                        } else {
                                            MaterialTheme.colorScheme.onSurfaceVariant
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
                // Menu button
                Box(
                    modifier = Modifier.padding(start = MaterialTheme.dimens.small)
                ) {
                    IconButton(
                        onClick = { showMenu = true },
                        modifier = Modifier.size(MaterialTheme.dimens.bigger)
                    ) {
                        Icon(
                            imageVector = Icons.Default.MoreVert,
                            contentDescription = stringResource(R.string.conversation_list_more_options),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 1f)
                        )
                    }

                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false },
                        offset = DpOffset(
                            x = (-MaterialTheme.dimens.medium),
                            y = MaterialTheme.dimens.none
                        ),
                        modifier = Modifier.background(MaterialTheme.colorScheme.surfaceContainerHighest)
                    ) {
                        val itemModifier = Modifier
                            .fillMaxWidth() // Ensure each item takes full width
                            .padding(vertical = MaterialTheme.dimens.none) // Reduce vertical padding between items

                        DropdownMenuItem(
                            text = {
                                Text(
                                    text = if (conversation.isBlockedByMe) stringResource(R.string.conversation_list_unblock_conversation_title) else stringResource(
                                        R.string.conversation_list_block_conversation_title
                                    )
                                )
                            },
                            onClick = {
                                showMenu = false
                                onBlockUser()
                            },
                            itemModifier
                        )
                        DropdownMenuItem(
                            text = {
                                Text(
                                    text = stringResource(R.string.chat_screen_clear_chat_title)
                                )
                            },
                            onClick = {
                                showMenu = false
                                onDeleteChat()
                            },
                            itemModifier
                        )
                    }
                }
            }
        }
    }
}