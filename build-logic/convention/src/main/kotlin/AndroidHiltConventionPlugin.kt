
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import se.zrcoding.convention.versionCatalog

class AndroidHiltConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("dagger.hilt.android.plugin")
                apply("com.google.devtools.ksp")
            }

            dependencies {
                add("implementation", versionCatalog().findLibrary("com.google.hilt").get())
                add("ksp", versionCatalog().findLibrary("com.google.hilt.compiler").get())
            }
        }
    }
}