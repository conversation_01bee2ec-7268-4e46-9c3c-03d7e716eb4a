package se.scmv.morocco.adstickybanner.network

import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query

data class Lead(
    val platform: String,
    val campaign: String,
    val tags: Tags
)

data class Tags(
    val name: String,
    val phone: String,
    val email: String,
    val city: String
)

data class LeadRequest(
    val leads: List<Lead>
)

data class LeadsApiResponse(
    val code: String,
    val req: List<String>,
)

interface LeadsRequestApi {
    @POST("ws/leads/store")
    fun storeLeads(
        @Query("reseller") reseller: String = ApiConstants.RESELLER,
        @Body request: LeadRequest
    ): Call<LeadsApiResponse>
}