package se.scmv.morocco.adstickybanner.domain

import kotlinx.coroutines.flow.StateFlow
import se.scmv.morocco.adstickybanner.models.NotifAd
import se.scmv.morocco.adstickybanner.network.LeadRequest

/**
 * Core domain interface for managing ad operations
 * Replaces the service-based architecture with a clean domain layer
 */
interface AdManager {
    
    /**
     * State flows for reactive UI updates
     */
    val imageSlideFlow: StateFlow<String>
    val notifAdFlow: StateFlow<NotifAd?>
    val isImageSlideActive: StateFlow<Boolean>
    val isNotificationAdActive: StateFlow<Boolean>
    
    /**
     * Ad lifecycle management
     */
    suspend fun startImageSlide(categoryId: String, cities: List<Int?>?)
    suspend fun stopImageSlide()
    suspend fun loadNotificationAds(categoryId: String, cities: List<Int?>?)
    suspend fun hideNotificationAd()
    
    /**
     * Analytics and tracking
     */
    suspend fun recordImpression()
    suspend fun recordClick(openWebViewActivity: (String) -> Unit)
    suspend fun recordCreativeImpression()
    suspend fun recordCreativeClick()
    
    /**
     * User interactions
     */
    suspend fun onNotificationAdCtaClick()
    suspend fun onStickyBannerClick()
    suspend fun storeLeads(leadRequest: LeadRequest)
    
    /**
     * State management
     */
    suspend fun updateCategory(categoryId: String, cities: List<Int?>?)
    suspend fun refreshAdStates()
    
    /**
     * Lifecycle management
     */
    fun initialize()
    fun cleanup()
}
