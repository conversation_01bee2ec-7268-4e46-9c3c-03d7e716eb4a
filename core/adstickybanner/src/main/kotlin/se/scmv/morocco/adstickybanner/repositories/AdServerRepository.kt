package se.scmv.morocco.adstickybanner.repositories

import se.scmv.morocco.adstickybanner.models.NotifAd
import se.scmv.morocco.adstickybanner.models.SlideAds
import se.scmv.morocco.adstickybanner.network.LeadRequest
import se.scmv.morocco.adstickybanner.network.LeadsApiResponse

interface AdServerRepository {
    suspend fun getActiveSlideAds(categoryId: String, cities: List<Int?>?): SlideAds?
    suspend fun getNotifAds(categoryId: String, cities: List<Int?>?): List<NotifAd>?
    suspend fun recordAdsClickOrImpression(campaignId: String, recordType: RecordType)
    fun storeLeads(leadRequest: LeadRequest): LeadsApiResponse
}

@JvmInline
value class RecordType private constructor(val value: String){
    companion object {
        val CLICK = RecordType("click_android")
        val CLICK_BUBBLE_ANDROID = RecordType("click_bubble_android")
        val CLICK_CREA_ANDROID = RecordType("click_crea_android")

        val IMPRESSION = RecordType("impression_android")
        val VIEW_BUBBLE_ANDROID = RecordType("impression_bubble_android")
        val VIEW_CREA_ANDROID = RecordType("impression_crea_android")
    }
}

@JvmInline value class AdServerFormat(val value: String){
    companion object {
        val SLIDE = AdServerFormat("slide")
        val NOTIF = AdServerFormat("notifad")
    }
}