package se.scmv.morocco.adstickybanner.domain.impl

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.adstickybanner.domain.AdManager
import se.scmv.morocco.adstickybanner.domain.AdStateRepository
import se.scmv.morocco.adstickybanner.models.NotifAd
import se.scmv.morocco.adstickybanner.network.LeadRequest
import se.scmv.morocco.adstickybanner.repositories.AdServerFormat
import se.scmv.morocco.adstickybanner.repositories.AdServerRepository
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AdManagerImpl @Inject constructor(
    private val adServerRepository: AdServerRepository,
    private val adStateRepository: AdStateRepository,
    private val slideAdUseCase: SlideAdUseCaseImpl,
    private val notificationAdUseCase: NotificationAdUseCaseImpl,
    private val adAnalyticsUseCase: AdAnalyticsUseCaseImpl
) : AdManager {

    companion object {
        private const val TAG = "AdManager"
    }

    // Coroutine scope for background operations
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // State flows for reactive UI updates
    private val _imageSlideFlow = MutableStateFlow("")
    override val imageSlideFlow: StateFlow<String> = _imageSlideFlow.asStateFlow()
    
    private val _notifAdFlow = MutableStateFlow<NotifAd?>(null)
    override val notifAdFlow: StateFlow<NotifAd?> = _notifAdFlow.asStateFlow()
    
    private val _isImageSlideActive = MutableStateFlow(false)
    override val isImageSlideActive: StateFlow<Boolean> = _isImageSlideActive.asStateFlow()
    
    private val _isNotificationAdActive = MutableStateFlow(false)
    override val isNotificationAdActive: StateFlow<Boolean> = _isNotificationAdActive.asStateFlow()
    
    // Session management
    private val adSessionFlow = MutableStateFlow(AdSession())
    private val urls = mutableListOf<String>()
    
    // Background jobs
    private var imageSlideJob: Job? = null
    private var adStateCheckJob: Job? = null

    override fun initialize() {
        Log.d(TAG, "Initializing AdManager")
        startAdStateCheck()
    }

    override fun cleanup() {
        Log.d(TAG, "Cleaning up AdManager")
        scope.cancel()
        imageSlideJob?.cancel()
        adStateCheckJob?.cancel()
    }

    override suspend fun startImageSlide(categoryId: String, cities: List<Int?>?) {
        imageSlideJob?.cancel() // Cancel previous job if running
        
        imageSlideJob = scope.launch {
            try {
                urls.clear()
                val imageResponse = slideAdUseCase.getActiveSlideAds(categoryId, cities)
                
                adSessionFlow.update { it.copy(
                    campaignId = imageResponse?.id,
                    adFormats = listOf(AdServerFormat.SLIDE),
                    urlAdToBrowse = imageResponse?.campaignData?.redirectLink
                ) }
                
                if (imageResponse != null && adStateRepository.shouldShowStickyBanner(imageResponse.id)) {
                    urls.addAll(imageResponse.campaignData.creative)
                    var index = 0
                    _isImageSlideActive.value = true
                    
                    while (true) {
                        _imageSlideFlow.value = urls[index]
                        index = (index + 1) % urls.size
                        delay(5000) // 5 second interval
                    }
                } else {
                    _imageSlideFlow.value = ""
                    _isImageSlideActive.value = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in image slide", e)
                _imageSlideFlow.value = ""
                _isImageSlideActive.value = false
            }
        }
    }

    override suspend fun stopImageSlide() {
        imageSlideJob?.cancel()
        _imageSlideFlow.value = ""
        _isImageSlideActive.value = false
        Log.d(TAG, "Stopped image slide")
    }

    override suspend fun loadNotificationAds(categoryId: String, cities: List<Int?>?) {
        scope.launch {
            try {
                val notifAds = notificationAdUseCase.getNotificationAds(categoryId, cities)
                Log.d(TAG, "Loaded notification ads: $notifAds")
                
                adSessionFlow.value = adSessionFlow.value.copy(
                    campaignId = notifAds?.firstOrNull()?.id,
                    adFormats = listOf(AdServerFormat.NOTIF)
                )
                
                if (!notifAds.isNullOrEmpty() && adStateRepository.shouldShowNotificationAd(notifAds[0].id)) {
                    _notifAdFlow.update { notifAds[0] }
                    _isNotificationAdActive.value = true
                } else {
                    _notifAdFlow.update { null }
                    _isNotificationAdActive.value = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading notification ads", e)
                _notifAdFlow.update { null }
                _isNotificationAdActive.value = false
            }
        }
    }

    override suspend fun hideNotificationAd() {
        _notifAdFlow.value = null
        _isNotificationAdActive.value = false
        Log.d(TAG, "Hidden notification ad")
    }

    override suspend fun recordImpression() {
        val session = adSessionFlow.value
        session.adFormats.forEach { format ->
            Log.d(TAG, "recordImpression: $format")
            when (format) {
                AdServerFormat.SLIDE -> {
                    if (adStateRepository.shouldShowStickyBanner(session.campaignId)) {
                        slideAdUseCase.recordSlideImpression(session.campaignId)
                    }
                }
                AdServerFormat.NOTIF -> {
                    if (adStateRepository.shouldShowNotificationAd(session.campaignId)) {
                        notificationAdUseCase.recordNotificationImpression(session.campaignId)
                    }
                }
            }
        }
    }

    override suspend fun recordClick(openWebViewActivity: (String) -> Unit) {
        val session = adSessionFlow.value
        session.adFormats.forEach { format ->
            when (format) {
                AdServerFormat.SLIDE -> {
                    slideAdUseCase.recordSlideClick(session.campaignId, openWebViewActivity)
                }
                AdServerFormat.NOTIF -> {
                    notificationAdUseCase.recordNotificationClick(session.campaignId)
                }
            }
        }
        
        if (session.urlAdToBrowse != null) {
            openWebViewActivity(session.urlAdToBrowse)
        }
    }

    override suspend fun recordCreativeImpression() {
        val session = adSessionFlow.value
        if (adStateRepository.shouldShowNotificationAd(session.campaignId)) {
            notificationAdUseCase.recordCreativeImpression(session.campaignId)
        }
    }

    override suspend fun recordCreativeClick() {
        val session = adSessionFlow.value
        if (adStateRepository.shouldShowNotificationAd(session.campaignId)) {
            notificationAdUseCase.recordCreativeClick(session.campaignId)
        }
    }

    override suspend fun onNotificationAdCtaClick() {
        val session = adSessionFlow.value
        adStateRepository.onNotificationAdCtaClick(session.campaignId)
        Log.d(TAG, "Notification ad CTA clicked for campaign: ${session.campaignId}")
    }

    override suspend fun onStickyBannerClick() {
        val session = adSessionFlow.value
        adStateRepository.onStickyBannerClick(session.campaignId)
        Log.d(TAG, "Sticky banner clicked for campaign: ${session.campaignId}")
    }

    override suspend fun storeLeads(leadRequest: LeadRequest) {
        notificationAdUseCase.storeLeads(leadRequest)
    }

    override suspend fun updateCategory(categoryId: String, cities: List<Int?>?) {
        val effectiveCategoryId = if (categoryId.isEmpty() || categoryId == "1") "home" else categoryId
        
        // Update both slide ads and notification ads
        startImageSlide(effectiveCategoryId, cities)
        loadNotificationAds(effectiveCategoryId, cities)
        
        Log.d(TAG, "Updated category to: $effectiveCategoryId")
    }

    override suspend fun refreshAdStates() {
        val session = adSessionFlow.value
        
        // Check if sticky banner should be shown/hidden for this campaign
        if (!adStateRepository.shouldShowStickyBanner(session.campaignId) && _imageSlideFlow.value.isNotEmpty()) {
            _imageSlideFlow.value = ""
            _isImageSlideActive.value = false
        }
        
        // Check if notification ad should be shown/hidden for this campaign
        if (!adStateRepository.shouldShowNotificationAd(session.campaignId) && _notifAdFlow.value != null) {
            _notifAdFlow.value = null
            _isNotificationAdActive.value = false
        }
        
        Log.d(TAG, "Refreshed ad states")
    }

    /**
     * Starts periodic ad state checking to update visibility
     */
    private fun startAdStateCheck() {
        adStateCheckJob?.cancel()
        adStateCheckJob = scope.launch {
            while (true) {
                delay(30000) // Check every 30 seconds
                refreshAdStates()
            }
        }
    }
}

/**
 * Data class for managing ad session state
 */
data class AdSession(
    val campaignId: String? = null,
    val adFormats: List<AdServerFormat?> = emptyList(),
    val urlAdToBrowse: String? = null
)
