package se.scmv.morocco.adstickybanner.domain.impl

import android.util.Log
import se.scmv.morocco.adstickybanner.domain.AdAnalyticsUseCase
import se.scmv.morocco.adstickybanner.domain.AdStateRepository
import se.scmv.morocco.adstickybanner.domain.AdStateUseCase
import se.scmv.morocco.adstickybanner.domain.NotificationAdUseCase
import se.scmv.morocco.adstickybanner.domain.SlideAdUseCase
import se.scmv.morocco.adstickybanner.models.NotifAd
import se.scmv.morocco.adstickybanner.models.SlideAds
import se.scmv.morocco.adstickybanner.network.LeadRequest
import se.scmv.morocco.adstickybanner.repositories.AdServerRepository
import se.scmv.morocco.adstickybanner.repositories.RecordType
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SlideAdUseCaseImpl @Inject constructor(
    private val adServerRepository: AdServerRepository,
    private val adStateRepository: AdStateRepository
) : SlideAdUseCase {
    
    companion object {
        private const val TAG = "SlideAdUseCase"
    }

    override suspend fun getActiveSlideAds(categoryId: String, cities: List<Int?>?): SlideAds? {
        return try {
            adServerRepository.getActiveSlideAds(categoryId, cities)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting active slide ads", e)
            null
        }
    }

    override suspend fun startImageSlide(categoryId: String, cities: List<Int?>?) {
        // This is handled by the AdManager implementation
        Log.d(TAG, "Starting image slide for category: $categoryId")
    }

    override suspend fun stopImageSlide() {
        // This is handled by the AdManager implementation
        Log.d(TAG, "Stopping image slide")
    }

    override suspend fun recordSlideImpression(campaignId: String?) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record slide impression: campaignId is null or empty")
            return
        }
        
        if (adStateRepository.shouldShowStickyBanner(campaignId)) {
            try {
                adServerRepository.recordAdsClickOrImpression(campaignId, RecordType.IMPRESSION)
                Log.d(TAG, "Recorded slide impression for campaign: $campaignId")
            } catch (e: Exception) {
                Log.e(TAG, "Error recording slide impression", e)
            }
        } else {
            Log.d(TAG, "Skipping slide impression recording - ad should not be shown for campaign: $campaignId")
        }
    }

    override suspend fun recordSlideClick(campaignId: String?, openWebViewActivity: (String) -> Unit) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record slide click: campaignId is null or empty")
            return
        }
        
        try {
            adServerRepository.recordAdsClickOrImpression(campaignId, RecordType.CLICK)
            Log.d(TAG, "Recorded slide click for campaign: $campaignId")
            
            // Get the redirect URL from the repository or session
            // This would need to be passed from the AdManager
            // For now, we'll just call the web view activity with a placeholder
            // openWebViewActivity("")
        } catch (e: Exception) {
            Log.e(TAG, "Error recording slide click", e)
        }
    }
}

@Singleton
class NotificationAdUseCaseImpl @Inject constructor(
    private val adServerRepository: AdServerRepository,
    private val adStateRepository: AdStateRepository
) : NotificationAdUseCase {
    
    companion object {
        private const val TAG = "NotificationAdUseCase"
    }

    override suspend fun getNotificationAds(categoryId: String, cities: List<Int?>?): List<NotifAd>? {
        return try {
            adServerRepository.getNotifAds(categoryId, cities)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting notification ads", e)
            null
        }
    }

    override suspend fun loadNotificationAds(categoryId: String, cities: List<Int?>?) {
        // This is handled by the AdManager implementation
        Log.d(TAG, "Loading notification ads for category: $categoryId")
    }

    override suspend fun hideNotificationAd() {
        // This is handled by the AdManager implementation
        Log.d(TAG, "Hiding notification ad")
    }

    override suspend fun recordNotificationImpression(campaignId: String?) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record notification impression: campaignId is null or empty")
            return
        }
        
        if (adStateRepository.shouldShowNotificationAd(campaignId)) {
            try {
                adServerRepository.recordAdsClickOrImpression(campaignId, RecordType.VIEW_BUBBLE_ANDROID)
                Log.d(TAG, "Recorded notification impression for campaign: $campaignId")
            } catch (e: Exception) {
                Log.e(TAG, "Error recording notification impression", e)
            }
        } else {
            Log.d(TAG, "Skipping notification impression recording - ad should not be shown for campaign: $campaignId")
        }
    }

    override suspend fun recordNotificationClick(campaignId: String?) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record notification click: campaignId is null or empty")
            return
        }
        
        try {
            adServerRepository.recordAdsClickOrImpression(campaignId, RecordType.CLICK_BUBBLE_ANDROID)
            Log.d(TAG, "Recorded notification click for campaign: $campaignId")
        } catch (e: Exception) {
            Log.e(TAG, "Error recording notification click", e)
        }
    }

    override suspend fun recordCreativeImpression(campaignId: String?) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record creative impression: campaignId is null or empty")
            return
        }
        
        if (adStateRepository.shouldShowNotificationAd(campaignId)) {
            try {
                adServerRepository.recordAdsClickOrImpression(campaignId, RecordType.VIEW_CREA_ANDROID)
                Log.d(TAG, "Recorded creative impression for campaign: $campaignId")
            } catch (e: Exception) {
                Log.e(TAG, "Error recording creative impression", e)
            }
        } else {
            Log.d(TAG, "Skipping creative impression recording - ad should not be shown for campaign: $campaignId")
        }
    }

    override suspend fun recordCreativeClick(campaignId: String?) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record creative click: campaignId is null or empty")
            return
        }
        
        if (adStateRepository.shouldShowNotificationAd(campaignId)) {
            try {
                adServerRepository.recordAdsClickOrImpression(campaignId, RecordType.CLICK_CREA_ANDROID)
                Log.d(TAG, "Recorded creative click for campaign: $campaignId")
            } catch (e: Exception) {
                Log.e(TAG, "Error recording creative click", e)
            }
        } else {
            Log.d(TAG, "Skipping creative click recording - ad should not be shown for campaign: $campaignId")
        }
    }

    override suspend fun storeLeads(leadRequest: LeadRequest) {
        try {
            adServerRepository.storeLeads(leadRequest)
            adStateRepository.onNotificationAdFormSubmit()
            Log.d(TAG, "Stored leads successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error storing leads", e)
        }
    }
}

@Singleton
class AdStateUseCaseImpl @Inject constructor(
    private val adStateRepository: AdStateRepository
) : AdStateUseCase {
    
    companion object {
        private const val TAG = "AdStateUseCase"
    }

    override fun shouldShowNotificationAd(campaignId: String?): Boolean {
        return adStateRepository.shouldShowNotificationAd(campaignId)
    }

    override fun shouldShowStickyBanner(campaignId: String?): Boolean {
        return adStateRepository.shouldShowStickyBanner(campaignId)
    }

    override fun onNotificationAdFormSubmit() {
        adStateRepository.onNotificationAdFormSubmit()
        Log.d(TAG, "Notification ad form submitted")
    }

    override fun onNotificationAdCtaClick(campaignId: String?) {
        adStateRepository.onNotificationAdCtaClick(campaignId)
        Log.d(TAG, "Notification ad CTA clicked for campaign: $campaignId")
    }

    override fun onStickyBannerClick(campaignId: String?) {
        adStateRepository.onStickyBannerClick(campaignId)
        Log.d(TAG, "Sticky banner clicked for campaign: $campaignId")
    }

    override fun resetAllAdStates() {
        adStateRepository.resetAllAdStates()
        Log.d(TAG, "Reset all ad states")
    }

    override fun resetCampaignTiming(campaignId: String) {
        adStateRepository.resetCampaignTiming(campaignId)
        Log.d(TAG, "Reset campaign timing for: $campaignId")
    }
}

@Singleton
class AdAnalyticsUseCaseImpl @Inject constructor(
    private val adServerRepository: AdServerRepository,
    private val adStateRepository: AdStateRepository
) : AdAnalyticsUseCase {
    
    companion object {
        private const val TAG = "AdAnalyticsUseCase"
    }

    override suspend fun recordImpression(campaignId: String?, adFormat: String) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record impression: campaignId is null or empty")
            return
        }
        
        val recordType = when (adFormat) {
            "slide" -> RecordType.IMPRESSION
            "notifad" -> RecordType.VIEW_BUBBLE_ANDROID
            else -> {
                Log.w(TAG, "Unknown ad format: $adFormat")
                return
            }
        }
        
        try {
            adServerRepository.recordAdsClickOrImpression(campaignId, recordType)
            Log.d(TAG, "Recorded impression for campaign: $campaignId, format: $adFormat")
        } catch (e: Exception) {
            Log.e(TAG, "Error recording impression", e)
        }
    }

    override suspend fun recordClick(campaignId: String?, adFormat: String, openWebViewActivity: (String) -> Unit) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record click: campaignId is null or empty")
            return
        }
        
        val recordType = when (adFormat) {
            "slide" -> RecordType.CLICK
            "notifad" -> RecordType.CLICK_BUBBLE_ANDROID
            else -> {
                Log.w(TAG, "Unknown ad format: $adFormat")
                return
            }
        }
        
        try {
            adServerRepository.recordAdsClickOrImpression(campaignId, recordType)
            Log.d(TAG, "Recorded click for campaign: $campaignId, format: $adFormat")
            
            // Handle web view opening if needed
            // openWebViewActivity("")
        } catch (e: Exception) {
            Log.e(TAG, "Error recording click", e)
        }
    }

    override suspend fun recordCreativeImpression(campaignId: String?) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record creative impression: campaignId is null or empty")
            return
        }
        
        if (adStateRepository.shouldShowNotificationAd(campaignId)) {
            try {
                adServerRepository.recordAdsClickOrImpression(campaignId, RecordType.VIEW_CREA_ANDROID)
                Log.d(TAG, "Recorded creative impression for campaign: $campaignId")
            } catch (e: Exception) {
                Log.e(TAG, "Error recording creative impression", e)
            }
        } else {
            Log.d(TAG, "Skipping creative impression recording - ad should not be shown for campaign: $campaignId")
        }
    }

    override suspend fun recordCreativeClick(campaignId: String?) {
        if (campaignId.isNullOrEmpty()) {
            Log.w(TAG, "Cannot record creative click: campaignId is null or empty")
            return
        }
        
        if (adStateRepository.shouldShowNotificationAd(campaignId)) {
            try {
                adServerRepository.recordAdsClickOrImpression(campaignId, RecordType.CLICK_CREA_ANDROID)
                Log.d(TAG, "Recorded creative click for campaign: $campaignId")
            } catch (e: Exception) {
                Log.e(TAG, "Error recording creative click", e)
            }
        } else {
            Log.d(TAG, "Skipping creative click recording - ad should not be shown for campaign: $campaignId")
        }
    }
}
