package se.scmv.morocco.adstickybanner.dtos

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import se.scmv.morocco.adstickybanner.models.CampaignData
import se.scmv.morocco.adstickybanner.models.SlideAds

@Keep
data class SlideAdsResponse(
    @SerializedName("ads") val ads: List<Ad>
){
    @Keep
    data class Ad(
        @SerializedName("_id") val id: String,
        //@SerializedName("isInactive") val isInactive: Boolean,
        //@SerializedName("type") val type: String,
        @SerializedName("campaignData") val campaignData: CampaignData,
        //@SerializedName("activityPeriod") val activityPeriod: ActivityPeriod,
        //@SerializedName("name") val name: String,
        //@SerializedName("client") val client: String,
        @SerializedName("targeting") val targeting: Targeting,
        //@SerializedName("dateCreated") val dateCreated: String,
        //@SerializedName("__v") val version: Int,
        //@SerializedName("stats") val stats: Stats,
        //@SerializedName("active") val active: Boolean
    )

    @Keep
    data class CampaignData(
        @SerializedName("creative") val creative: List<String>,
        @SerializedName("redirectLink") val redirectLink: String
    )

    @Keep
    data class ActivityPeriod(
        @SerializedName("from") val from: String,
        @SerializedName("to") val to: String
    )

    @Keep
    data class Targeting(
        @SerializedName("categories") val categories: List<Category>,
        @SerializedName("cities") val cities: List<Category>?
    )

    @Keep
    data class Category(
        @SerializedName("value") val value: String,
        @SerializedName("label") val label: String
    )

    @Keep
    data class Stats(
        @SerializedName("impression_msite") val impressionMsite: Map<String, Int>,
        @SerializedName("click_msite") val clickMsite: Map<String, Int>,
        @SerializedName("impression_bubble_msite") val impressionBubbleMsite: Map<String, Int>?
    )
}



fun SlideAdsResponse.Ad.toSlideAds() =
    SlideAds(
        id = this.id,
        campaignData = CampaignData(
            creative = this.campaignData.creative,
            redirectLink = this.campaignData.redirectLink
        ),
        platforms = emptyList(),
        targeting = this.targeting.categories.map { it.value } // Extracting category values
    )


fun List<SlideAds>.toImageUrls() = this.map { it.campaignData.creative }.flatten()