package se.scmv.morocco.adstickybanner.network

import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import se.scmv.morocco.adstickybanner.dtos.NotifAdResponse
import se.scmv.morocco.adstickybanner.dtos.SlideAdsResponse

interface AdServerAdvertisingApi {
    @GET("campaigns?type=slide&active=true")
    suspend fun getActiveSlideAds(): Response<SlideAdsResponse>

    @GET("campaigns?type=notifad&active=true")
    suspend fun getNotifAds(): Response<NotifAdResponse>

    @GET("campaigns/{campaignId}/stats/{clickOrImpression}")
    suspend fun recordAds(
        @Path("campaignId") campaignId: String,
        @Path("clickOrImpression") clickOrImpression: String,
    ): Response<Unit>
}