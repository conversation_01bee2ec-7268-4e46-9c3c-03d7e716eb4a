package se.scmv.morocco.adstickybanner.presentation

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animate
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import kotlinx.coroutines.delay
import se.scmv.morocco.adstickybanner.models.NotifAd
import se.scmv.morocco.designsystem.theme.dimens
import kotlin.math.roundToInt


//NotifAd
@Composable
fun BubbleOverlayAd(
    modifier: Modifier = Modifier,
    notifAd: NotifAd,
    onDismiss: () -> Unit = {},
    onNotifAdClick: (String) -> Unit = {},
    onFormSubmit: (String, String, String, String) -> Unit = { _, _, _, _ -> },
    onActionClick: (String) -> Unit = { },
) {
    var isExpanded by remember { mutableStateOf(false) }

    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp
    val screenWidth = configuration.screenWidthDp.dp
    val density = LocalDensity.current

    var offsetX by remember { mutableFloatStateOf(
        with(density) {(screenWidth - 90.dp).toPx()}
    ) }
    var offsetY by remember { mutableFloatStateOf(
        with(density) {(screenHeight * 0.3f).toPx()}
    ) }

    var isDragging by remember { mutableStateOf(false) }

    val dismissZoneHeight = screenHeight * 0.2f
    val dismissZoneY = screenHeight - dismissZoneHeight

    val current = LocalDensity.current

    val bubbleSize by animateFloatAsState(
        targetValue = if (isExpanded) 0f else 70f,
        label = "bubbleSize",
        animationSpec = tween(300)
    )

    var isBubbleVisible by remember { mutableStateOf(true) }

    var isAnimatingDismissal by remember { mutableStateOf(false) }
    var dismissProgress by remember { mutableFloatStateOf(1f) }

    // Message bubble state
    var isMessageBubbleVisible by remember { mutableStateOf(false) }

    // Infinite pulse animation for the main bubble
    val infiniteTransition = rememberInfiniteTransition(label = "pulse")
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.7f,
        targetValue = 0.9f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse"
    )

    // Show message bubble after 2 seconds (reduced for testing)
    LaunchedEffect(Unit) {
        isMessageBubbleVisible = true
        delay(5000)
        isMessageBubbleVisible = false
    }

    LaunchedEffect(isAnimatingDismissal) {
        if (isAnimatingDismissal) {
            animate(
                initialValue = 1f,
                targetValue = 0f,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            ) { value, _ ->
                dismissProgress = value
                if (value <= 0f) {

                    isBubbleVisible = false
                    onDismiss()
                }
            }
        }
    }

    if (!isBubbleVisible) {
        return
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .zIndex(10f)
    ) {
        if (!isExpanded) {
            // Debug: Always show message bubble for testing
            if(isMessageBubbleVisible){
                // Subtle pulse animation for message bubble
                val messagePulseScale by infiniteTransition.animateFloat(
                    initialValue = 0.7f,
                    targetValue = 0.9f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1000, easing = FastOutSlowInEasing),
                        repeatMode = RepeatMode.Reverse
                    ),
                    label = "messagePulse"
                )
                
                // Entrance animation for message bubble
                val messageEntranceScale by animateFloatAsState(
                    targetValue = if (isMessageBubbleVisible) 1f else 0f,
                    animationSpec = tween(500, easing = FastOutSlowInEasing),
                    label = "messageEntrance"
                )
                
                // Calculate position relative to the main bubble
                val bubbleLeftEdge = offsetX // Left edge of main bubble
                val messageBubbleX = bubbleLeftEdge - with(density) { 180.dp.toPx() } // Position to the left of main bubble
                val messageBubbleY = offsetY - with(density) { 30.dp.toPx() } // Slightly above main bubble

                MessageBubble(
                    message = notifAd.message.ifEmpty { "Check out this amazing offer! 🎉" },
                    backgroundColor = notifAd.backgroundColor,
                    modifier = Modifier
                        .offset { IntOffset(messageBubbleX.roundToInt(), messageBubbleY.roundToInt()) }
                        .zIndex(20f) // Higher z-index to ensure visibility
                        .scale(messagePulseScale * messageEntranceScale) // Apply both pulse and entrance animation
                )
            }

            Box(
                modifier = Modifier
                    .offset { IntOffset(offsetX.roundToInt(), offsetY.roundToInt()) }
                    .clip(CircleShape)
                    .padding(10.dp)
                    .size(bubbleSize.dp)
                    .scale(dismissProgress * pulseScale) // Apply pulse animation
                    .alpha(dismissProgress)
                    .background(
                        color = notifAd.backgroundColor.hexToColor().copy(alpha = 0.3f),
                        shape = CircleShape
                    )
                    .clickable(enabled = !isDragging) {
                        isExpanded = true
                        onNotifAdClick(notifAd.id)
                    }
                    .pointerInput(Unit) {
                        detectDragGestures(
                            onDragStart = { isDragging = true },
                            onDragEnd = {
                                isDragging = false

                                val bubbleY = offsetY + bubbleSize
                                val dismissThreshold = with(current) {
                                    dismissZoneY.toPx()
                                }

                                if (bubbleY > dismissThreshold) {
                                    isAnimatingDismissal = true
                                }
                            },
                            onDragCancel = { isDragging = false },
                            onDrag = { change, dragAmount ->
                                change.consume()
                                offsetX += dragAmount.x
                                offsetY += dragAmount.y
                            }
                        )
                    },
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .size(80.dp)
                        .clip(CircleShape)
                        .background(color = notifAd.backgroundColor.hexToColor())
                        .border(5.dp, notifAd.backgroundColor.hexToColor(), CircleShape)
                        .padding(5.dp)
                ) {
                    AsyncImage(
                        model = notifAd.thumbnailCreative,
                        contentDescription = "",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.White),
                    )
                }

                Box(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(22.dp)
                        .background(Color.Red, CircleShape)
                        .border(1.5.dp, Color.White, CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "1",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                }
            }

            if (isDragging) {
                val pulseAnimation by rememberInfiniteTransition(label = "pulse").animateFloat(
                    initialValue = 0.9f,
                    targetValue = 1.1f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1000),
                        repeatMode = RepeatMode.Reverse
                    ),
                    label = "pulse"
                )

                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 20.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(50.dp)
                            .scale(pulseAnimation)
                            .clip(CircleShape)
                            .background(Color.Red.copy(alpha = 0.6f))
                            .border(1.dp, Color.White, CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Dismiss",
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }

        AnimatedVisibility(
            visible = isExpanded,
            enter = fadeIn() + expandVertically(),
            exit = fadeOut() + shrinkVertically()
        ) {
            Box(
                modifier = Modifier
                    .background(Color.Black.copy(alpha = 0.4f))
                    .fillMaxSize()
                    .clickable(enabled = false) { },
                contentAlignment = Alignment.Center
            ) {
                    ExpandedAdContent(
                        notifAd = notifAd,
                        cta = notifAd.cta ?: "",
                        isFormType = notifAd.type != "crea",
                        onFormSubmit = { name, phone, email, city ->
                            isExpanded = false
                            onFormSubmit(name, phone, email, city)
                        },
                        onDismiss = {
                            isExpanded = false
                            onDismiss()
                        },
                        onActionClick = { url ->
                            isExpanded = false
                            onActionClick(url)
                        },
                        rowBackgroundColor = notifAd.backgroundColor
                    )
            }
        }
    }
}

@Composable
fun MessageBubble(
    message: String,
    backgroundColor: String,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    
    // Responsive dimensions for message bubble
    val maxWidth = (screenWidth * 0.7f).coerceAtMost(280.dp)
    val minWidth = 140.dp // Increased minimum width
    val padding = (screenWidth * 0.02f).coerceAtLeast(12.dp).coerceAtMost(10.dp)
    val fontSize = (screenWidth.value * 0.45f).let {
        if (it < 14) 14.sp else if (it > 18) 18.sp else it.sp // Increased font size
    }
    
    Card(
        modifier = modifier
            .widthIn(min = minWidth, max = maxWidth),
        shape = RoundedCornerShape(
            topStart = 16.dp,
            topEnd = 16.dp,
            bottomStart = 16.dp,
            bottomEnd = 4.dp
        ),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor.hexToColor()
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp) // Increased elevation for better shadow
    ) {
        Box(
            modifier = Modifier
                .padding(padding)
        ) {
            // Message bubble tail pointing to the right (towards main bubble)
            Box(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .offset(x = 4.dp, y = 0.dp)
                    .size(8.dp)
                    .background(
                        color = backgroundColor.hexToColor(),
                        shape = RoundedCornerShape(topEnd = 4.dp, bottomEnd = 4.dp)
                    )
            )
            
            Text(
                text = message,
                color = Color.White,
                fontSize = fontSize,
                fontWeight = FontWeight.SemiBold, // Made text bolder
                textAlign = TextAlign.Center,
                lineHeight = fontSize * 1.2f
            )
        }
    }
}

@Composable
fun ExpandedAdContent(
    notifAd: NotifAd,
    isFormType: Boolean = false,
    cta: String,
    onDismiss: () -> Unit,
    onFormSubmit: (String, String, String, String) -> Unit = { _, _, _, _ -> },
    onActionClick: (String) -> Unit = { },
    rowBackgroundColor: String
) {
    val infiniteTransition = rememberInfiniteTransition(label = "")

    val rotation by infiniteTransition.animateFloat(
        initialValue = -5f,
        targetValue = 5f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 300, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ), label = ""
    )
    
    // Get screen dimensions for responsive design
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    val screenHeight = configuration.screenHeightDp.dp
    
    // Calculate responsive dimensions
    val cardMaxWidth = (screenWidth * 0.95f).coerceAtMost(400.dp)
    val cardMaxHeight = (screenHeight * 0.85f).coerceAtMost(700.dp)
    val imageMaxHeight = (screenHeight * 0.6f).coerceAtMost(600.dp)
    val headerPadding = (screenWidth * 0.02f).coerceAtLeast(8.dp)
    val contentPadding = (screenWidth * 0.04f).coerceAtLeast(16.dp)
    val thumbnailSize = (screenWidth * 0.1f).coerceAtLeast(32.dp).coerceAtMost(48.dp)
    val closeButtonSize = (screenWidth * 0.06f).coerceAtLeast(20.dp).coerceAtMost(28.dp)
    val ctaFontSize = (screenWidth.value * 0.05f).let {
        if (it < 16) 16.sp else if (it > 24) 24.sp else it.sp
    }
    
    Card(
        modifier = Modifier
            .width(cardMaxWidth)
            .height(cardMaxHeight),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
            Column(
                modifier = Modifier
                    .background(notifAd.backgroundColor.hexToColor())
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(rowBackgroundColor.hexToColor())
                        .padding(headerPadding),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = notifAd.thumbnailCreative ,
                        contentDescription = "",
                        modifier = Modifier
                            .size(thumbnailSize)
                            .background(notifAd.backgroundColor.hexToColor())
                            .clip(RoundedCornerShape(8.dp))
                    )

                    Spacer(modifier = Modifier.width(headerPadding))

                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = notifAd.clientName,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            fontSize = (screenWidth.value * 0.035f).let { 
                                if (it < 14) 14.sp else if (it > 18) 18.sp else it.sp
                            }
                        )
                        Text(
                            text = notifAd.title,
                            color = MaterialTheme.colorScheme.onPrimary,
                            fontSize = (screenWidth.value * 0.025f).let { 
                                if (it < 10) 10.sp else if (it > 14) 14.sp else it.sp
                            }
                        )
                    }
                    IconButton(
                        onClick = { onDismiss() },
                        modifier = Modifier.size(closeButtonSize)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Close",
                            tint = Color.White,
                            modifier = Modifier.size(closeButtonSize * 0.7f)
                        )
                    }
                }
                
                if (isFormType){
                    val viewModel: LeadFormViewModel = hiltViewModel()
                    val cities by viewModel.cities.collectAsState()
                    var selectedCity by remember { mutableStateOf<se.scmv.morocco.domain.models.City?>(null) }
                    if (cities.isEmpty()) {
                        CircularProgressIndicator()
                    } else {
                        CreaForm(
                            heading = notifAd.heading,
                            subheading = notifAd.subHeading,
                            creativeUrl = notifAd.bigCreative,
                            cities = cities,
                            selectedCity = selectedCity,
                            onCitySelected = { selectedCity = it },
                            cityError = null,
                            onSubmit = { name, phone, email, city ->
                                onFormSubmit(name, phone, email, city.name)
                            }
                        )
                    }
                } else {
                    AsyncImage(
                        model = notifAd.bigCreative,
                        contentDescription = "Ad Banner",
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(imageMaxHeight)
                            .padding(contentPadding),
                        contentScale = ContentScale.Fit
                    )
                }

                Spacer(modifier = Modifier.weight(1f))

                TextButton(
                    modifier = Modifier
                        .clip(RoundedCornerShape(10.dp))
                        .graphicsLayer(rotationZ = rotation),
                    onClick = {
                        onActionClick(notifAd.redirectLink)
                    }) {
                    Text(
                        text = cta,
                        color = Color.White,
                        fontSize = ctaFontSize,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier
                            .background(Color.Black.copy(alpha = 0.8f))
                            .padding(
                                MaterialTheme.dimens.medium
                            )
                    )
                }
                
                Spacer(modifier = Modifier.height(contentPadding))
            }
    }
}

fun String.hexToColor(): Color {
    try {
        val cleanHex = this.removePrefix("#")
        val colorLong = cleanHex.toLong(16) or 0xFF000000

        return Color(colorLong)
    }catch (e: Exception){
        return Color.Gray
    }
}

fun String.openUrl(context: Context) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(this))
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    context.startActivity(intent)
}