package se.scmv.morocco.adstickybanner.domain

/**
 * Repository interface for managing ad state persistence
 * Extracted from AdStateManager to follow clean architecture principles
 */
interface AdStateRepository {
    
    /**
     * Notification ad state management
     */
    fun shouldShowNotificationAd(campaignId: String? = null): Boolean
    fun onNotificationAdFormSubmit()
    fun onNotificationAdCtaClick(campaignId: String? = null)
    fun getNotificationAdRemainingHideTimeMinutes(campaignId: String? = null): Long
    
    /**
     * Sticky banner state management
     */
    fun shouldShowStickyBanner(campaignId: String? = null): Boolean
    fun onStickyBannerClick(campaignId: String? = null)
    fun getStickyBannerRemainingHideTimeMinutes(campaignId: String? = null): Long
    
    /**
     * State reset operations
     */
    fun resetAllAdStates()
    fun resetCampaignTiming(campaignId: String)
    
    /**
     * State observation
     */
    fun isNotificationAdHidden(): Boolean
    fun isStickyBannerHidden(): Boolean
}
