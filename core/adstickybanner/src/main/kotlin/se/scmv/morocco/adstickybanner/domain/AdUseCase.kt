package se.scmv.morocco.adstickybanner.domain

import se.scmv.morocco.adstickybanner.models.NotifAd
import se.scmv.morocco.adstickybanner.models.SlideAds
import se.scmv.morocco.adstickybanner.network.LeadRequest

/**
 * Use case for managing slide ads
 */
interface SlideAdUseCase {
    suspend fun getActiveSlideAds(categoryId: String, cities: List<Int?>?): SlideAds?
    suspend fun startImageSlide(categoryId: String, cities: List<Int?>?)
    suspend fun stopImageSlide()
    suspend fun recordSlideImpression(campaignId: String?)
    suspend fun recordSlideClick(campaignId: String?, openWebViewActivity: (String) -> Unit)
}

/**
 * Use case for managing notification ads
 */
interface NotificationAdUseCase {
    suspend fun getNotificationAds(categoryId: String, cities: List<Int?>?): List<NotifAd>?
    suspend fun loadNotificationAds(categoryId: String, cities: List<Int?>?)
    suspend fun hideNotificationAd()
    suspend fun recordNotificationImpression(campaignId: String?)
    suspend fun recordNotificationClick(campaignId: String?)
    suspend fun recordCreativeImpression(campaignId: String?)
    suspend fun recordCreativeClick(campaignId: String?)
    suspend fun storeLeads(leadRequest: LeadRequest)
}

/**
 * Use case for managing ad state
 */
interface AdStateUseCase {
    fun shouldShowNotificationAd(campaignId: String? = null): Boolean
    fun shouldShowStickyBanner(campaignId: String? = null): Boolean
    fun onNotificationAdFormSubmit()
    fun onNotificationAdCtaClick(campaignId: String? = null)
    fun onStickyBannerClick(campaignId: String? = null)
    fun resetAllAdStates()
    fun resetCampaignTiming(campaignId: String)
}

/**
 * Use case for managing ad analytics
 */
interface AdAnalyticsUseCase {
    suspend fun recordImpression(campaignId: String?, adFormat: String)
    suspend fun recordClick(campaignId: String?, adFormat: String, openWebViewActivity: (String) -> Unit)
    suspend fun recordCreativeImpression(campaignId: String?)
    suspend fun recordCreativeClick(campaignId: String?)
}
