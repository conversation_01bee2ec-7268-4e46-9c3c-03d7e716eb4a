package se.scmv.morocco.adstickybanner.domain.impl

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.core.content.edit
import dagger.hilt.android.qualifiers.ApplicationContext
import se.scmv.morocco.adstickybanner.domain.AdStateRepository
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AdStateRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : AdStateRepository {
    
    companion object {
        private const val TAG = "AdStateRepository"
        private const val PREFS_NAME = "ad_state_prefs"

        private const val KEY_NOTIF_AD_PERMANENTLY_HIDDEN = "notif_ad_permanently_hidden"
        private const val KEY_NOTIF_AD_HIDE_UNTIL = "notif_ad_hide_until"
        private const val KEY_NOTIF_AD_BACKOFF_COUNT = "notif_ad_backoff_count"
        private const val KEY_STICKY_BANNER_HIDE_UNTIL = "sticky_banner_hide_until"
        private const val KEY_STICKY_BANNER_BACKOFF_COUNT = "sticky_banner_backoff_count"
        
        // New keys for campaignId-based timing
        private const val KEY_PREFIX_NOTIF_AD_HIDE_UNTIL = "notif_ad_hide_until_campaign_"
        private const val KEY_PREFIX_NOTIF_AD_BACKOFF_COUNT = "notif_ad_backoff_count_campaign_"
        private const val KEY_PREFIX_STICKY_BANNER_HIDE_UNTIL = "sticky_banner_hide_until_campaign_"
        private const val KEY_PREFIX_STICKY_BANNER_BACKOFF_COUNT = "sticky_banner_backoff_count_campaign_"
        
        // Exponential backoff configuration (in minutes)
        private val BACKOFF_MINUTES = listOf(1, 2, 4, 8, 16, 32, 64, 128, 256, 512)
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    override fun shouldShowNotificationAd(campaignId: String?): Boolean {
        if (campaignId.isNullOrBlank()) {
            if (prefs.getBoolean(KEY_NOTIF_AD_PERMANENTLY_HIDDEN, false)) {
                return false
            }

            val hideUntil = prefs.getLong(KEY_NOTIF_AD_HIDE_UNTIL, 0L)
            if (hideUntil > System.currentTimeMillis()) {
                return false
            }

            if (hideUntil > 0 && hideUntil <= System.currentTimeMillis()) {
                prefs.edit { putLong(KEY_NOTIF_AD_HIDE_UNTIL, 0L) }
            }

            return true
        }

        if (prefs.getBoolean(KEY_NOTIF_AD_PERMANENTLY_HIDDEN, false)) {
            return false
        }
        
        // Check campaign-specific timing
        val keyHideUntil = "$KEY_PREFIX_NOTIF_AD_HIDE_UNTIL$campaignId"
        val hideUntil = prefs.getLong(keyHideUntil, 0L)
        
        if (hideUntil > System.currentTimeMillis()) {
            return false
        }
        
        // Reset backoff count if hide period has expired
        if (hideUntil > 0 && hideUntil <= System.currentTimeMillis()) {
            prefs.edit { putLong(keyHideUntil, 0L) }
        }
        
        return true
    }

    override fun shouldShowStickyBanner(campaignId: String?): Boolean {
        // Check campaign-specific timing
        val keyHideUntil = "$KEY_PREFIX_STICKY_BANNER_HIDE_UNTIL$campaignId"
        val hideUntil = prefs.getLong(keyHideUntil, 0L)

        if (hideUntil > System.currentTimeMillis()) {
            return false
        }

        // Reset backoff count if hide period has expired
        if (hideUntil > 0 && hideUntil <= System.currentTimeMillis()) {
            prefs.edit { putLong(keyHideUntil, 0L) }
        }

        return true
    }

    override fun onNotificationAdFormSubmit() {
        Log.d(TAG, "Notification Ad form submitted - permanently hiding ad")
        prefs.edit { putBoolean(KEY_NOTIF_AD_PERMANENTLY_HIDDEN, true) }
    }

    override fun onNotificationAdCtaClick(campaignId: String?) {
        val keyBackoffCount = "$KEY_PREFIX_NOTIF_AD_BACKOFF_COUNT$campaignId"
        val keyHideUntil = "$KEY_PREFIX_NOTIF_AD_HIDE_UNTIL$campaignId"

        val currentBackoffCount = prefs.getInt(keyBackoffCount, 0)
        val nextBackoffCount = (currentBackoffCount + 1).coerceAtMost(BACKOFF_MINUTES.size - 1)
        val hideMinutes = BACKOFF_MINUTES[nextBackoffCount]
        val hideUntil = System.currentTimeMillis() + (hideMinutes * 60 * 1000L)

        Log.d(TAG, "Notification Ad CTA clicked for campaign $campaignId - hiding for $hideMinutes minutes (backoff count: $nextBackoffCount)")

        prefs.edit {
            putLong(keyHideUntil, hideUntil)
            putInt(keyBackoffCount, nextBackoffCount)
        }
    }

    override fun onStickyBannerClick(campaignId: String?) {
        val keyBackoffCount = "$KEY_PREFIX_STICKY_BANNER_BACKOFF_COUNT$campaignId"
        val keyHideUntil = "$KEY_PREFIX_STICKY_BANNER_HIDE_UNTIL$campaignId"

        val currentBackoffCount = prefs.getInt(keyBackoffCount, 0)
        val nextBackoffCount = (currentBackoffCount + 1).coerceAtMost(BACKOFF_MINUTES.size - 1)
        val hideMinutes = BACKOFF_MINUTES[nextBackoffCount]
        val hideUntil = System.currentTimeMillis() + (hideMinutes * 60 * 1000L)

        Log.d(TAG, "Sticky Banner clicked for campaign $campaignId - hiding for $hideMinutes minutes (backoff count: $nextBackoffCount)")

        prefs.edit {
            putLong(keyHideUntil, hideUntil)
                .putInt(keyBackoffCount, nextBackoffCount)
        }
    }

    override fun getNotificationAdRemainingHideTimeMinutes(campaignId: String?): Long {
        val keyHideUntil = "$KEY_PREFIX_NOTIF_AD_HIDE_UNTIL$campaignId"
        val hideUntil = prefs.getLong(keyHideUntil, 0L)
        if (hideUntil <= System.currentTimeMillis()) return 0L
        return (hideUntil - System.currentTimeMillis()) / (60 * 1000L)
    }

    override fun getStickyBannerRemainingHideTimeMinutes(campaignId: String?): Long {
        val keyHideUntil = "$KEY_PREFIX_STICKY_BANNER_HIDE_UNTIL$campaignId"
        val hideUntil = prefs.getLong(keyHideUntil, 0L)
        if (hideUntil <= System.currentTimeMillis()) return 0L
        return (hideUntil - System.currentTimeMillis()) / (60 * 1000L)
    }

    override fun resetAllAdStates() {
        Log.d(TAG, "Resetting all ad states")
        prefs.edit {
            putBoolean(KEY_NOTIF_AD_PERMANENTLY_HIDDEN, false)
                .putLong(KEY_NOTIF_AD_HIDE_UNTIL, 0L)
                .putInt(KEY_NOTIF_AD_BACKOFF_COUNT, 0)
                .putLong(KEY_STICKY_BANNER_HIDE_UNTIL, 0L)
                .putInt(KEY_STICKY_BANNER_BACKOFF_COUNT, 0)
        }
    }

    override fun resetCampaignTiming(campaignId: String) {
        if (campaignId.isBlank()) return
        
        Log.d(TAG, "Resetting timing for campaign $campaignId")
        
        val notifHideKey = "$KEY_PREFIX_NOTIF_AD_HIDE_UNTIL$campaignId"
        val notifBackoffKey = "$KEY_PREFIX_NOTIF_AD_BACKOFF_COUNT$campaignId"
        val stickyHideKey = "$KEY_PREFIX_STICKY_BANNER_HIDE_UNTIL$campaignId"
        val stickyBackoffKey = "$KEY_PREFIX_STICKY_BANNER_BACKOFF_COUNT$campaignId"
        
        prefs.edit {
            putLong(notifHideKey, 0L)
                .putInt(notifBackoffKey, 0)
                .putLong(stickyHideKey, 0L)
                .putInt(stickyBackoffKey, 0)
        }
    }

    override fun isNotificationAdHidden(): Boolean {
        return !shouldShowNotificationAd()
    }

    override fun isStickyBannerHidden(): Boolean {
        return !shouldShowStickyBanner()
    }
}
