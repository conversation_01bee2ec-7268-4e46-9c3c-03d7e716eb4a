package se.scmv.morocco.adstickybanner.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.adstickybanner.domain.AdAnalyticsUseCase
import se.scmv.morocco.adstickybanner.domain.AdManager
import se.scmv.morocco.adstickybanner.domain.AdStateRepository
import se.scmv.morocco.adstickybanner.domain.AdStateUseCase
import se.scmv.morocco.adstickybanner.domain.NotificationAdUseCase
import se.scmv.morocco.adstickybanner.domain.SlideAdUseCase
import se.scmv.morocco.adstickybanner.domain.impl.AdAnalyticsUseCaseImpl
import se.scmv.morocco.adstickybanner.domain.impl.AdManagerImpl
import se.scmv.morocco.adstickybanner.domain.impl.AdStateRepositoryImpl
import se.scmv.morocco.adstickybanner.domain.impl.AdStateUseCaseImpl
import se.scmv.morocco.adstickybanner.domain.impl.NotificationAdUseCaseImpl
import se.scmv.morocco.adstickybanner.domain.impl.SlideAdUseCaseImpl
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AdModule {

    @Provides
    @Singleton
    fun provideAdStateRepository(
        impl: AdStateRepositoryImpl
    ): AdStateRepository = impl

    @Provides
    @Singleton
    fun provideSlideAdUseCase(
        impl: SlideAdUseCaseImpl
    ): SlideAdUseCase = impl

    @Provides
    @Singleton
    fun provideNotificationAdUseCase(
        impl: NotificationAdUseCaseImpl
    ): NotificationAdUseCase = impl

    @Provides
    @Singleton
    fun provideAdStateUseCase(
        impl: AdStateUseCaseImpl
    ): AdStateUseCase = impl

    @Provides
    @Singleton
    fun provideAdAnalyticsUseCase(
        impl: AdAnalyticsUseCaseImpl
    ): AdAnalyticsUseCase = impl

    @Provides
    @Singleton
    fun provideAdManager(
        impl: AdManagerImpl
    ): AdManager = impl
}
