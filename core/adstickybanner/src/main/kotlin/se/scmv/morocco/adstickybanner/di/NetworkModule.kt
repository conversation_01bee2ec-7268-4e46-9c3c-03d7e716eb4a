package se.scmv.morocco.adstickybanner.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import se.scmv.morocco.adstickybanner.BuildConfig
import se.scmv.morocco.adstickybanner.network.AdServerAdvertisingApi
import se.scmv.morocco.adstickybanner.network.ApiConstants
import se.scmv.morocco.adstickybanner.network.LeadsRequestApi
import javax.inject.Named
import javax.inject.Singleton
import javax.net.ssl.SSLContext

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Singleton
    @Provides
    fun provideHttpLoggingInterceptor() = HttpLoggingInterceptor().apply {
        if (BuildConfig.DEBUG) {
            level = HttpLoggingInterceptor.Level.BODY
        }
    }

    @Singleton
    @Provides
    fun provideOkHttpClient(loggingInterceptor: HttpLoggingInterceptor) = OkHttpClient.Builder()
        .addInterceptor(loggingInterceptor)
        .build()

    @Singleton
    @Provides
    @Named("AdServerRetrofit")
    fun provideAdServerRetrofit(okHttpClient: OkHttpClient): Retrofit = provideRetrofit(
        okHttpClient = okHttpClient,
        baseUrl = "https://media-server.avito.ma/"
    )

    @Singleton
    @Provides
    @Named("LeadsForceOkHttpClient")
    fun provideLeadsForceOkHttpClient(loggingInterceptor: HttpLoggingInterceptor): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .addInterceptor { chain ->
                val request = chain.request().newBuilder()
                    .addHeader(ApiConstants.HEADER_API_TOKEN, ApiConstants.API_TOKEN)
                    .build()
                chain.proceed(request)
            }
            .build()
    }

    @Singleton
    @Provides
    @Named("LeadsForceRetrofit")
    fun provideLeadsForceRetrofit(@Named("LeadsForceOkHttpClient") okHttpClient: OkHttpClient): Retrofit = provideRetrofit(
        okHttpClient = okHttpClient,
        baseUrl = "https://api.leadsforce.ma/"
    )

    @Singleton
    @Provides
    fun provideLeadsForceApi(
        @Named("LeadsForceRetrofit") retrofit: Retrofit
    ): LeadsRequestApi = retrofit.create(LeadsRequestApi::class.java)


    @Singleton
    @Provides
    fun provideAdServerAdvertisingApi(
        @Named("AdServerRetrofit") retrofit: Retrofit
    ): AdServerAdvertisingApi = retrofit.create(AdServerAdvertisingApi::class.java)


    private fun provideRetrofit(okHttpClient: OkHttpClient, baseUrl: String) = Retrofit.Builder()
        .baseUrl(baseUrl)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()


    @Singleton
    @Provides
    fun provideSSLContext(): SSLContext = SSLContext.getInstance("TLSv1.2")
}