package se.scmv.morocco.adstickybanner

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class AdStateManagerTest {

    private lateinit var mockContext: Context
    private lateinit var mockPrefs: SharedPreferences
    private lateinit var mockEditor: SharedPreferences.Editor
    private lateinit var adStateManager: AdStateManager

    @Before
    fun setUp() {
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns 0
        
        mockContext = mockk()
        mockPrefs = mockk()
        mockEditor = mockk()
        
        every { mockContext.getSharedPreferences(any(), any()) } returns mockPrefs
        every { mockPrefs.edit() } returns mockEditor
        every { mockEditor.putBoolean(any(), any()) } returns mockEditor
        every { mockEditor.putLong(any(), any()) } returns mockEditor
        every { mockEditor.putInt(any(), any()) } returns mockEditor
        every { mockEditor.apply() } returns Unit
        
        // Default mock configurations for constructor
        every { mockPrefs.getBoolean(any(), false) } returns false
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        every { mockPrefs.getInt(any(), 0) } returns 0
        
        adStateManager = AdStateManager(mockContext)
    }

    @Test
    fun `should show notification ad by default`() {
        // Given: Default state (no preferences set)
        every { mockPrefs.getBoolean(any(), false) } returns false
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When & Then
        assertTrue(adStateManager.shouldShowNotificationAd())
    }

    @Test
    fun `should not show notification ad when permanently hidden`() {
        // Given: Ad is permanently hidden
        every { mockPrefs.getBoolean(any(), false) } returns true
        
        // When & Then
        assertFalse(adStateManager.shouldShowNotificationAd())
    }

    @Test
    fun `should not show notification ad when temporarily hidden`() {
        // Given: Ad is temporarily hidden (future timestamp)
        val futureTime = System.currentTimeMillis() + 60000 // 1 minute from now
        every { mockPrefs.getBoolean(any(), false) } returns false
        every { mockPrefs.getLong(any(), 0L) } returns futureTime
        
        // When & Then
        assertFalse(adStateManager.shouldShowNotificationAd())
    }

    @Test
    fun `should show notification ad when hide period expired`() {
        // Given: Hide period has expired (past timestamp)
        val pastTime = System.currentTimeMillis() - 60000 // 1 minute ago
        every { mockPrefs.getBoolean(any(), false) } returns false
        every { mockPrefs.getLong(any(), 0L) } returns pastTime
        
        // When & Then
        assertTrue(adStateManager.shouldShowNotificationAd())
    }

    @Test
    fun `should show sticky banner by default`() {
        // Given: Default state (no preferences set)
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When & Then
        assertTrue(adStateManager.shouldShowStickyBanner())
    }

    @Test
    fun `should not show sticky banner when temporarily hidden`() {
        // Given: Banner is temporarily hidden (future timestamp)
        val futureTime = System.currentTimeMillis() + 60000 // 1 minute from now
        every { mockPrefs.getLong(any(), 0L) } returns futureTime
        
        // When & Then
        assertFalse(adStateManager.shouldShowStickyBanner())
    }

    @Test
    fun `should permanently hide notification ad on form submit`() {
        // Given: Default state
        every { mockPrefs.getBoolean(any(), false) } returns false
        
        // When
        adStateManager.onNotificationAdFormSubmit()
        
        // Then
        verify { mockEditor.putBoolean("notif_ad_permanently_hidden", true) }
        verify { mockEditor.apply() }
        
        // Update the mock to reflect the change
        every { mockPrefs.getBoolean("notif_ad_permanently_hidden", false) } returns true
        assertFalse(adStateManager.shouldShowNotificationAd())
    }

    @Test
    fun `should hide sticky banner with exponential backoff on click`() {
        // Given: Default state
        every { mockPrefs.getInt(any(), 0) } returns 0
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When
        adStateManager.onStickyBannerClick()
        
        // Then: Should hide for 5 minutes (first backoff level)
        verify { mockEditor.putLong("sticky_banner_hide_until", any()) }
        verify { mockEditor.putInt("sticky_banner_backoff_count", 1) }
        verify { mockEditor.apply() }
        
        // Update the mock to reflect the change (future timestamp)
        every { mockPrefs.getLong("sticky_banner_hide_until", 0L) } returns System.currentTimeMillis() + 300000L // 5 minutes
        assertFalse(adStateManager.shouldShowStickyBanner())
    }

    @Test
    fun `should increase backoff duration on subsequent clicks`() {
        // Given: Already clicked once (backoff count = 1)
        every { mockPrefs.getInt("sticky_banner_backoff_count", 0) } returns 1
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When
        adStateManager.onStickyBannerClick()
        
        // Then: Should hide for 10 minutes (second backoff level)
        verify { mockEditor.putInt("sticky_banner_backoff_count", 2) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `should reset all ad states`() {
        // When
        adStateManager.resetAllAdStates()
        
        // Then
        verify { mockEditor.putBoolean("notif_ad_permanently_hidden", false) }
        verify { mockEditor.putLong("notif_ad_hide_until", 0L) }
        verify { mockEditor.putInt("notif_ad_backoff_count", 0) }
        verify { mockEditor.putLong("sticky_banner_hide_until", 0L) }
        verify { mockEditor.putInt("sticky_banner_backoff_count", 0) }
        verify { mockEditor.apply() }
    }

    // New tests for campaignId-based functionality

    @Test
    fun `should show notification ad for specific campaign by default`() {
        // Given: Default state for a specific campaign
        val campaignId = "test_campaign_123"
        every { mockPrefs.getBoolean(any(), false) } returns false
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When & Then
        assertTrue(adStateManager.shouldShowNotificationAd(campaignId))
    }

    @Test
    fun `should not show notification ad for specific campaign when permanently hidden globally`() {
        // Given: Ad is permanently hidden globally
        val campaignId = "test_campaign_123"
        every { mockPrefs.getBoolean("notif_ad_permanently_hidden", false) } returns true
        
        // When & Then
        assertFalse(adStateManager.shouldShowNotificationAd(campaignId))
    }

    @Test
    fun `should not show notification ad for specific campaign when temporarily hidden`() {
        // Given: Campaign-specific ad is temporarily hidden
        val campaignId = "test_campaign_123"
        val futureTime = System.currentTimeMillis() + 60000 // 1 minute from now
        every { mockPrefs.getBoolean(any(), false) } returns false
        every { mockPrefs.getLong("notif_ad_hide_until_campaign_$campaignId", 0L) } returns futureTime
        
        // When & Then
        assertFalse(adStateManager.shouldShowNotificationAd(campaignId))
    }

    @Test
    fun `should show notification ad for specific campaign when hide period expired`() {
        // Given: Campaign-specific hide period has expired
        val campaignId = "test_campaign_123"
        val pastTime = System.currentTimeMillis() - 60000 // 1 minute ago
        every { mockPrefs.getBoolean(any(), false) } returns false
        every { mockPrefs.getLong("notif_ad_hide_until_campaign_$campaignId", 0L) } returns pastTime
        
        // When & Then
        assertTrue(adStateManager.shouldShowNotificationAd(campaignId))
    }

    @Test
    fun `should show sticky banner for specific campaign by default`() {
        // Given: Default state for a specific campaign
        val campaignId = "test_campaign_123"
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When & Then
        assertTrue(adStateManager.shouldShowStickyBanner(campaignId))
    }

    @Test
    fun `should not show sticky banner for specific campaign when temporarily hidden`() {
        // Given: Campaign-specific banner is temporarily hidden
        val campaignId = "test_campaign_123"
        val futureTime = System.currentTimeMillis() + 60000 // 1 minute from now
        every { mockPrefs.getLong("sticky_banner_hide_until_campaign_$campaignId", 0L) } returns futureTime
        
        // When & Then
        assertFalse(adStateManager.shouldShowStickyBanner(campaignId))
    }

    @Test
    fun `should hide notification ad for specific campaign with exponential backoff on click`() {
        // Given: Default state for a specific campaign
        val campaignId = "test_campaign_123"
        every { mockPrefs.getInt("notif_ad_backoff_count_campaign_$campaignId", 0) } returns 0
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When
        adStateManager.onNotificationAdCtaClick(campaignId)
        
        // Then: Should hide for 1 minute (first backoff level)
        verify { mockEditor.putLong("notif_ad_hide_until_campaign_$campaignId", any()) }
        verify { mockEditor.putInt("notif_ad_backoff_count_campaign_$campaignId", 1) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `should hide sticky banner for specific campaign with exponential backoff on click`() {
        // Given: Default state for a specific campaign
        val campaignId = "test_campaign_123"
        every { mockPrefs.getInt("sticky_banner_backoff_count_campaign_$campaignId", 0) } returns 0
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When
        adStateManager.onStickyBannerClick(campaignId)
        
        // Then: Should hide for 1 minute (first backoff level)
        verify { mockEditor.putLong("sticky_banner_hide_until_campaign_$campaignId", any()) }
        verify { mockEditor.putInt("sticky_banner_backoff_count_campaign_$campaignId", 1) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `should increase backoff duration for specific campaign on subsequent clicks`() {
        // Given: Already clicked once for a specific campaign (backoff count = 1)
        val campaignId = "test_campaign_123"
        every { mockPrefs.getInt("sticky_banner_backoff_count_campaign_$campaignId", 0) } returns 1
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When
        adStateManager.onStickyBannerClick(campaignId)
        
        // Then: Should hide for 2 minutes (second backoff level)
        verify { mockEditor.putInt("sticky_banner_backoff_count_campaign_$campaignId", 2) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `should fall back to legacy behavior for blank campaignId`() {
        // Given: Blank campaignId
        val blankCampaignId = ""
        every { mockPrefs.getBoolean(any(), false) } returns false
        every { mockPrefs.getLong(any(), 0L) } returns 0L
        
        // When & Then: Should use legacy format-based methods
        assertTrue(adStateManager.shouldShowNotificationAd(blankCampaignId))
        assertTrue(adStateManager.shouldShowStickyBanner(blankCampaignId))
    }

    @Test
    fun `should reset timing for specific campaign`() {
        // Given: A specific campaign
        val campaignId = "test_campaign_123"
        
        // When
        adStateManager.resetCampaignTiming(campaignId)
        
        // Then
        verify { mockEditor.putLong("notif_ad_hide_until_campaign_$campaignId", 0L) }
        verify { mockEditor.putInt("notif_ad_backoff_count_campaign_$campaignId", 0) }
        verify { mockEditor.putLong("sticky_banner_hide_until_campaign_$campaignId", 0L) }
        verify { mockEditor.putInt("sticky_banner_backoff_count_campaign_$campaignId", 0) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `should get remaining hide time for specific notification ad`() {
        // Given: Campaign-specific notification ad is hidden
        val campaignId = "test_campaign_123"
        val futureTime = System.currentTimeMillis() + 120000 // 2 minutes from now
        every { mockPrefs.getLong("notif_ad_hide_until_campaign_$campaignId", 0L) } returns futureTime
        
        // When
        val remainingMinutes = adStateManager.getNotificationAdRemainingHideTimeMinutes(campaignId)
        
        // Then: Should return approximately 2 minutes
        assertTrue(remainingMinutes >= 1 && remainingMinutes <= 3) // Allow for timing variations
    }

    @Test
    fun `should get remaining hide time for specific sticky banner`() {
        // Given: Campaign-specific sticky banner is hidden
        val campaignId = "test_campaign_123"
        val futureTime = System.currentTimeMillis() + 180000 // 3 minutes from now
        every { mockPrefs.getLong("sticky_banner_hide_until_campaign_$campaignId", 0L) } returns futureTime
        
        // When
        val remainingMinutes = adStateManager.getStickyBannerRemainingHideTimeMinutes(campaignId)
        
        // Then: Should return approximately 3 minutes
        assertTrue(remainingMinutes >= 2 && remainingMinutes <= 4) // Allow for timing variations
    }

    @Test
    fun `should handle different campaigns independently`() {
        // Given: Two different campaigns
        val campaign1 = "campaign_1"
        val campaign2 = "campaign_2"
        
        // Campaign 1 is hidden
        every { mockPrefs.getLong("notif_ad_hide_until_campaign_$campaign1", 0L) } returns System.currentTimeMillis() + 60000
        // Campaign 2 is not hidden
        every { mockPrefs.getLong("notif_ad_hide_until_campaign_$campaign2", 0L) } returns 0L
        every { mockPrefs.getBoolean(any(), false) } returns false
        
        // When & Then
        assertFalse(adStateManager.shouldShowNotificationAd(campaign1))
        assertTrue(adStateManager.shouldShowNotificationAd(campaign2))
    }
} 