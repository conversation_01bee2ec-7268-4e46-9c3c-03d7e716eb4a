plugins {
    id("avito.android.library")
    id("avito.android.library.compose")
    id("avito.android.hilt")
}

android {
    namespace = "se.scmv.morocco.analytics"

    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    implementation(project(":core:common"))

    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.compose.runtime)

    implementation(libs.appBoy)
    implementation(libs.com.facebook.sdk)
    implementation(platform(libs.com.google.firebase.bom))
    implementation(libs.com.google.firebase.analytics)
    //... other dependencies
    implementation(libs.tiktok.business)
    implementation (libs.androidx.lifecycle.common.java8)
}