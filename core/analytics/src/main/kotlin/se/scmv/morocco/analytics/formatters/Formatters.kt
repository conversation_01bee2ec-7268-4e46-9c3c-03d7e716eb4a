package se.scmv.morocco.analytics.formatters

import android.os.Bundle
import com.braze.models.outgoing.BrazeProperties
import se.scmv.morocco.analytics.models.Param

/**
 * Converts a set of [Param] objects into a Braze-specific [BrazeProperties] object.
 *
 * This extension function iterates over each [Param] in the set and adds it to the
 * [BrazeProperties] object as a key-value pair, where the key is the param's name
 * and the value is its corresponding value.
 *
 * @return A [BrazeProperties] object containing all the parameters as properties.
 */
fun Set<Param>.toBrazeProperties() = BrazeProperties().apply {
    <EMAIL> {
        addProperty(it.key, it.value)
    }
}


/**
 * Converts a set of [Param] objects into a [Bundle].
 *
 * This extension function iterates over each [Param] in the set and adds it to the
 * [Bundle] as a key-value pair, where the key is the param's name and the value is
 * its corresponding value.
 *
 * @return A [Bundle] containing all the parameters as key-value pairs.
 */
fun Set<Param>.toBundle() = Bundle().apply {
    <EMAIL> { param ->
        putString(param.key, param.value)
    }
}

/**
 * Converts a set of [Param] objects into a [Map] with [String] keys and values.
 *
 * This extension function iterates over each [Param] in the set and adds it to the
 * map as a key-value pair, where both the key and value are strings.
 *
 * @return A [Map] of strings representing the parameters as key-value pairs.
 */
fun Set<Param>.toMap(): Map<String, String> = mutableMapOf<String, String>().apply {
    <EMAIL> { param ->
        put(param.key, param.value)
    }
}