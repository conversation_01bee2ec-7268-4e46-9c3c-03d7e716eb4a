package se.scmv.morocco.analytics.di

import android.annotation.SuppressLint
import android.content.Context
import com.braze.Braze
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.BuildConfig
import se.scmv.morocco.analytics.impl.AnalyticsHelperImpl
import se.scmv.morocco.analytics.impl.AnalyticsHelperStubImpl
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AnalyticsModule {

    @Provides
    @Singleton
    fun provideAppBoy(@ApplicationContext context: Context): Braze {
        return Braze.getInstance(context)
    }

    @SuppressLint("MissingPermission")
    @Provides
    @Singleton
    fun provideFirebaseAnalytics(@ApplicationContext context: Context): FirebaseAnalytics {
        return FirebaseAnalytics.getInstance(context)
    }

    @Provides
    @Singleton
    fun provideFacebookAnalytics(@ApplicationContext context: Context): AppEventsLogger {
        return AppEventsLogger.newLogger(context)
    }

    /**
     * Provides an instance of [AnalyticsHelper], which will vary depending on the build type.
     *
     * In debug builds, this returns an instance of [AnalyticsHelperStubImpl] for mock/stub implementations.
     * In release builds, it returns a fully functional [AnalyticsHelperImpl] that integrates
     * with Braze, Firebase Analytics, and Facebook Analytics.
     * To test events in Firebase debug view remove the if condition and provide AnalyticsHelperImpl.
     *
     * @param appboy An instance of [Braze] for Braze analytics.
     * @param firebaseAnalytics An instance of [FirebaseAnalytics] for Firebase analytics.
     * @param appEventsLogger An instance of [AppEventsLogger] for Facebook analytics.
     * @return An implementation of [AnalyticsHelper] based on the build type.
     */
    @Provides
    fun bindsAnalyticsHelper(
        braze: Braze,
        firebaseAnalytics: FirebaseAnalytics,
        appEventsLogger: AppEventsLogger,
    ): AnalyticsHelper {
        return if (BuildConfig.DEBUG) AnalyticsHelperStubImpl()
        else AnalyticsHelperImpl(
            braze = braze,
            facebookLogger = appEventsLogger,
            firebaseAnalytics = firebaseAnalytics,
        )
    }
}
