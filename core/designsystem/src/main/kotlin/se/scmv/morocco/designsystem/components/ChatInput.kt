package se.scmv.morocco.designsystem.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.Blue600
import se.scmv.morocco.designsystem.theme.Gray100
import se.scmv.morocco.designsystem.theme.Gray400
import se.scmv.morocco.designsystem.theme.Gray500
import se.scmv.morocco.designsystem.theme.Red500
import se.scmv.morocco.designsystem.theme.White
import se.scmv.morocco.domain.models.Attachment

/**
 * Generic attachment data class for the design system
 */
data class ChatAttachment(
    val id: String,
    val filePath: String,
    val previewUri: String,
    val type: String,
    val fileName: String? = null
)

@Composable
fun ChatInput(
    text: String,
    onTextChange: (String) -> Unit,
    onSendMessage: () -> Unit,
    onAttachmentClick: () -> Unit,
    isBlocked: Boolean,
    isSending: Boolean,
    modifier: Modifier = Modifier,
    attachments: List<Attachment> = emptyList(),
    onRemoveAttachment: (Attachment) -> Unit = {},
    placeholderText: String = stringResource(R.string.write_message_here),
    showAttachmentButton: Boolean = true
) {
    Column(
        modifier = modifier
            .padding(horizontal = 8.dp, vertical = 8.dp)
    ) {
        if (attachments.isNotEmpty()) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = if (showAttachmentButton) 50.dp else 0.dp)
            ) {
                attachments.forEach { attachment ->
                    Box(
                        modifier = Modifier
                            .padding(end = 8.dp)
                            .size(40.dp) // Ensure fixed square size
                    ) {
                        if (attachment.type.startsWith("image/")) {
                            Box(
                                modifier = Modifier
                                    .matchParentSize()
                                    .clip(RoundedCornerShape(8.dp))
                            ) {
                                AsyncImage(
                                    model = attachment.previewUri,
                                    contentDescription = null,
                                    contentScale = ContentScale.Crop,
                                    modifier = Modifier.matchParentSize()
                                )
                            }
                        } else {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .background(Gray100, RoundedCornerShape(8.dp))
                                    .padding(8.dp)
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_attachment),
                                    contentDescription = null,
                                    tint = Blue600
                                )
                                Text(
                                    text = attachment.filePath.substringAfterLast('/'),
                                    style = MaterialTheme.typography.bodySmall,
                                    modifier = Modifier.padding(start = 4.dp)
                                )
                            }
                        }

                        // Floating dismiss icon
                        Box(
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .size(16.dp) // Set the background size
                                .background(White.copy(alpha = 0.8f), shape = CircleShape)
                                .clickable { onRemoveAttachment(attachment) },
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Remove attachment",
                                tint = Red500,
                                modifier = Modifier.size(12.dp) // Set the icon size
                            )
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.height(8.dp))
        }
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            if (showAttachmentButton) {
                IconButton(
                    onClick = onAttachmentClick,
                    enabled = !isBlocked && !isSending,
                    colors = IconButtonDefaults.filledTonalIconButtonColors(
                        containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
                        contentColor = if (!isBlocked && !isSending) Blue600 else Gray400
                    ),
                    modifier = Modifier
                        .size(50.dp)
                        .clip(CircleShape)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add attachment",
                        tint = if (!isBlocked && !isSending) Blue600 else Gray400
                    )
                }
                Spacer(modifier = Modifier.width(4.dp))
            }
            Box(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 0.dp, vertical = 0.dp),
                contentAlignment = Alignment.CenterStart
            ) {
                Spacer(modifier = Modifier.height(6.dp))
                BasicTextField(
                    value = text,
                    onValueChange = onTextChange,
                    singleLine = false,
                    enabled = !isBlocked && !isSending,
                    textStyle = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onSurface
                    ),
                    cursorBrush = SolidColor(Blue600),
                    maxLines = 6,
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 50.dp)
                        .background(MaterialTheme.colorScheme.background, RoundedCornerShape(24.dp))
                        .border(1.dp, MaterialTheme.colorScheme.outline, RoundedCornerShape(24.dp))
                        .padding(horizontal = 14.dp, vertical = 10.dp),
                    decorationBox = { innerTextField ->
                        Box(
                            Modifier
                                .fillMaxWidth(),
                            contentAlignment = Alignment.CenterStart
                        ) {
                            if (text.isEmpty()) {
                                Text(
                                    placeholderText,
                                    color = Gray500,
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontWeight = FontWeight.Normal
                                    ),
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }
                            innerTextField()
                        }
                    }
                )
                Spacer(modifier = Modifier.height(6.dp))
                if (isSending) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .size(28.dp),
                        strokeWidth = 2.dp,
                        color = Blue600
                    )
                }
            }
            Spacer(modifier = Modifier.width(8.dp))
            IconButton(
                onClick = onSendMessage,
                modifier = Modifier
                    .size(50.dp)
                    .clip(CircleShape),
                colors = if ((text.isNotBlank() || attachments.isNotEmpty()) && !isBlocked && !isSending) {
                    IconButtonDefaults.filledIconButtonColors(
                        containerColor = Blue600,
                        contentColor = White
                    )
                } else {
                    IconButtonDefaults.filledTonalIconButtonColors(
                        containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
                        contentColor = Blue600
                    )
                },
                enabled = (text.isNotBlank() || attachments.isNotEmpty()) && !isBlocked && !isSending
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Default.Send,
                    contentDescription = "Send message",
                    tint = if ((text.isNotBlank() || attachments.isNotEmpty()) && !isBlocked && !isSending) White else Blue600
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewChatInput() {
    val (text, setText) = remember { mutableStateOf("") }
    ChatInput(
        text = text,
        onTextChange = setText,
        onSendMessage = {},
        onAttachmentClick = {},
        isBlocked = false,
        isSending = false,
        attachments = emptyList(),
        onRemoveAttachment = {},
        modifier = Modifier.fillMaxWidth()
    )
}

@Preview(showBackground = true, name = "ChatInput with Attachments")
@Composable
fun PreviewChatInputWithAttachments() {
    val (text, setText) = remember { mutableStateOf("") }
    val attachments = listOf(
        Attachment(
            filePath = "/storage/emulated/0/Download/sample_image.jpg",
            previewUri = "https://via.placeholder.com/40",
            type = "image/jpeg",
        ),
        Attachment(
            filePath = "/storage/emulated/0/Download/sample_file.pdf",
            previewUri = "",
            type = "application/pdf",
        )
    )
    ChatInput(
        text = text,
        onTextChange = setText,
        onSendMessage = {},
        onAttachmentClick = {},
        isBlocked = false,
        isSending = false,
        attachments = attachments,
        onRemoveAttachment = {},
        modifier = Modifier.fillMaxWidth()
    )
}

@Preview(showBackground = true, name = "ChatInput without Attachment Button")
@Composable
fun PreviewChatInputWithoutAttachmentButton() {
    val (text, setText) = remember { mutableStateOf("") }
    ChatInput(
        text = text,
        onTextChange = setText,
        onSendMessage = {},
        onAttachmentClick = {},
        isBlocked = false,
        isSending = false,
        attachments = emptyList(),
        onRemoveAttachment = {},
        showAttachmentButton = false,
        modifier = Modifier.fillMaxWidth()
    )
}
