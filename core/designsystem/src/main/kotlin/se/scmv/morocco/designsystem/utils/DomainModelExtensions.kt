package se.scmv.morocco.designsystem.utils

import androidx.compose.ui.graphics.Color
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.domain.models.AccountAdDeactivationReason
import se.scmv.morocco.domain.models.AccountAdDeactivationSoldOnSiteDuration
import se.scmv.morocco.domain.models.MyAccountAdStatus
import se.scmv.morocco.domain.models.MyEditAdStatus
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.roundToInt
import kotlin.time.Duration.Companion.seconds

fun MyAccountAdStatus?.bgColor(): Color = when (this) {
    MyAccountAdStatus.DELETED -> Color(0XFFf2f2f2)
    MyAccountAdStatus.DEACTIVATED -> Color(0XFFebf0fe)
    MyAccountAdStatus.REFUSED -> Color(0XFFF7EBEA)
    MyAccountAdStatus.ACTIVE -> Color(0XFFEBF5F0)
    else -> Color(0XFFfdf6eb)
}

fun MyAccountAdStatus?.textColor(): Color = when (this) {
    MyAccountAdStatus.DELETED -> Color(0XFF666666)
    MyAccountAdStatus.DEACTIVATED -> Color(0XFF2E6BFF)
    MyAccountAdStatus.REFUSED -> Color(0XFFD13649)
    MyAccountAdStatus.ACTIVE -> Color(0XFF29A160)
    else -> Color(0XFFF5A623)
}

fun MyEditAdStatus?.bgColor(): Color = when (this) {
    MyEditAdStatus.USER_EDIT_REFUSED -> Color(0XFFF7EBEA)
    MyEditAdStatus.USER_EDIT_PENDING_REVIEW -> Color(0XFFebf0fe)
    else -> Color(0XFFEBF5F0)
}

fun MyEditAdStatus?.textColor(): Color = when (this) {
    MyEditAdStatus.USER_EDIT_REFUSED -> Color(0XFFD13649)
    MyEditAdStatus.USER_EDIT_PENDING_REVIEW -> Color(0XFF2E6BFF)
    else -> Color(0XFF29A160)
}


fun LocalDateTime.toRelativeTimeRes(): Int {
    val timeZone = TimeZone.UTC
    val now = Clock.System.now().toLocalDateTime(timeZone)

    val nowInstant = now.toInstant(timeZone)
    val thisInstant = this.toInstant(timeZone)
    val diff = nowInstant - thisInstant

    val diffMinutes = diff.inWholeMinutes
    val diffDays = diff.inWholeDays
    val diffHoursRounded = (diffMinutes / 60.0).roundToInt()

    return when {
        diffMinutes < 1 -> R.string.just_now
        diffMinutes < 60 -> R.string.minutes_ago
        diffHoursRounded < 24 -> R.string.hours_ago
        diffDays == 1L -> R.string.yesterday
        diffDays < 7 -> R.string.days_ago
        diffDays < 30 -> R.string.weeks_ago
        diffDays < 365 -> R.string.months_ago
        else -> R.string.years_ago
    }
}


fun LocalDateTime.relativeValue(): Int {
    val timeZone = TimeZone.UTC
    val now = Clock.System.now().toLocalDateTime(timeZone)

    val nowInstant = now.toInstant(timeZone)
    val thisInstant = this.toInstant(timeZone)
    val diff = nowInstant - thisInstant

    val diffMinutes = diff.inWholeMinutes
    val diffHours = diff.inWholeHours
    val diffDays = diff.inWholeDays
    val diffHoursRounded = (diffMinutes / 60.0).roundToInt()

    return when {
        diffHours < 1 -> diffMinutes.toInt()
        diffDays < 1 -> diffHoursRounded
        diffDays < 7 -> diffDays.toInt()
        diffDays < 30 -> (diffDays / 7).toInt()
        diffDays < 365 -> (diffDays / 30).toInt()
        else -> (diffDays / 365).toInt()
    }
}


fun LocalDateTime.toFormattedString(pattern: String = "yyyy-MM-dd'T'HH:mm:ss"): String {
    val javaLocalDateTime = java.time.LocalDateTime.of(
        this.year, this.monthNumber, this.dayOfMonth,
        this.hour, this.minute, this.second, this.nanosecond
    )
    val formatter = DateTimeFormatter.ofPattern(pattern)
    return javaLocalDateTime.format(formatter)
}

fun String.parseDateToReadableFormat(): String {
    return try {
        // Input formatter for ISO 8601 format
        val inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssX")
            .withZone(ZoneId.of("UTC"))

        // Parse the string to a ZonedDateTime
        val parsedDate = ZonedDateTime.parse(this, inputFormatter)

        // Output formatter for a readable format, staying in UTC
        val outputFormatter = DateTimeFormatter.ofPattern("dd MMM yyyy, HH:mm")
            .withZone(ZoneId.of("UTC"))

        // Format the parsed date
        parsedDate.format(outputFormatter)
    } catch (e: Exception) {
        // Return an empty string in case of an error
        ""
    }
}


fun String.getBoostStatus(expirationDate: String?): Int {
    return try {
        val inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssX")
            .withZone(ZoneId.of("UTC"))

        // Parse the start date and expiration date
        val startDateTime = ZonedDateTime.parse(this, inputFormatter)
        val expirationDateTime = ZonedDateTime.parse(expirationDate, inputFormatter)

        val currentDateTime = ZonedDateTime.now(ZoneId.of("UTC"))

        // Determine the boost status
        when {
            startDateTime.isAfter(currentDateTime) -> 0 // Planned Boost (start date is in the future)
            expirationDateTime.isBefore(currentDateTime) -> 2 // Expired Boost (expiration date is in the past)
            else -> 1 // Active Boost (currently ongoing)
        }
    } catch (e: Exception) {
        -1 // Return -1 in case of parsing failure
    }
}


fun LocalDateTime.addDaysWithoutPlus(days: Int): LocalDateTime {
    val instant = this.toInstant(TimeZone.UTC)
    val durationInSeconds = days * 24L * 3600L
    val newInstant = instant + durationInSeconds.seconds
    return newInstant.toLocalDateTime(TimeZone.UTC)
}

fun LocalDateTime?.calculateRemainingSecondsToDelete(): Int {
    return try {
        if (this == null) return -1

        val currentDateTime = Clock.System.now().toLocalDateTime(TimeZone.UTC)
        val deleteAllowedDateTime = this.addDaysWithoutPlus(7)

        if (currentDateTime < deleteAllowedDateTime) {
            val diffInSeconds =
                (deleteAllowedDateTime.toInstant(TimeZone.UTC) - currentDateTime.toInstant(TimeZone.UTC)).inWholeSeconds
            diffInSeconds.toInt()
        } else {
            0
        }
    } catch (e: Exception) {
        -1
    }
}


fun MyAccountAdStatus.localizedNameRes(): Int = when (this) {
    MyAccountAdStatus.DEACTIVATED -> R.string.deactivated
    MyAccountAdStatus.REFUSED -> R.string.refused
    MyAccountAdStatus.DELETED -> R.string.deleted
    MyAccountAdStatus.PENDING_PAYMENT -> R.string.pending_payment
    MyAccountAdStatus.PENDING_REVIEW -> R.string.pending_review
    MyAccountAdStatus.BOOSTED_ACTIVE_ADS -> R.string.boosted_active_ads
    else -> R.string.active
}

fun MyEditAdStatus.localizedNameRes(): Int {
    return when (this) {
        MyEditAdStatus.USER_EDIT_PENDING_REVIEW -> R.string.user_edit_pending_review
        MyEditAdStatus.USER_EDIT_ACCEPTED -> R.string.user_edit_accepted
        MyEditAdStatus.USER_EDIT_REFUSED -> R.string.user_edit_refused
    }
}

fun AccountAdDeactivationSoldOnSiteDuration.localizedNameRes(): Int = when (this) {
    AccountAdDeactivationSoldOnSiteDuration.ONE_DAY -> R.string.one_day
    AccountAdDeactivationSoldOnSiteDuration.ONE_WEEK -> R.string.one_week
    AccountAdDeactivationSoldOnSiteDuration.ONE_MONTH -> R.string.one_month
    AccountAdDeactivationSoldOnSiteDuration.OVER_ONE_MONTH -> R.string.over_one_month
    else -> R.string.dont_remember
}

// Extension function for localized string resource IDs
fun AccountAdDeactivationReason.localizedResId(): Int {
    return when (this) {
        AccountAdDeactivationReason.SOLD_ON_SITE -> R.string.sold_on_site
        AccountAdDeactivationReason.SOLD_OTHER_MEANS -> R.string.sold_other_means
        AccountAdDeactivationReason.EDIT_OR_CHANGE -> R.string.edit_or_change
        AccountAdDeactivationReason.RENEW_OR_BUMP -> R.string.renew_or_bump
        AccountAdDeactivationReason.GIVE_UP -> R.string.give_up
        AccountAdDeactivationReason.OTHER -> R.string.other
    }
}

fun AccountAdDeactivationSoldOnSiteDuration.localizedResId(): Int {
    return when (this) {
        AccountAdDeactivationSoldOnSiteDuration.ONE_DAY -> R.string.one_day
        AccountAdDeactivationSoldOnSiteDuration.ONE_WEEK -> R.string.one_week
        AccountAdDeactivationSoldOnSiteDuration.ONE_MONTH -> R.string.one_month
        AccountAdDeactivationSoldOnSiteDuration.OVER_ONE_MONTH -> R.string.over_one_month
        AccountAdDeactivationSoldOnSiteDuration.DONT_REMEMBER -> R.string.dont_remember
    }
}














