package se.scmv.morocco.designsystem.components.filter

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.FilterChip
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.theme.Gray150
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters.Field

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AvChipMultiSelectExtended(
    fields: List<Field> = emptyList(),
    selectedChips: MutableList<Field>,  // Allow selectedChips to be mutable for easier updates
    onSelectedItem: (Field) -> Unit
) {
    var chipsVisibility by remember { mutableStateOf(false) }
    var iconButtonVector by remember { mutableStateOf(Icons.Default.KeyboardArrowDown) }
    var isSelected: Int = -1
    Log.d("MultiSelectExtended", "AvChipMultiSelectExtended called")
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(
                end = MaterialTheme.dimens.medium,
                top = MaterialTheme.dimens.big,
                start = MaterialTheme.dimens.medium
            )
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.padding(
                    start = MaterialTheme.dimens.medium
                ),
                text = "Détails supplémentaire",
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                fontSize = MaterialTheme.typography.titleMedium.fontSize
            )
            OutlinedButton(
                modifier = Modifier.size(25.dp),
                shape = CircleShape,
                border = BorderStroke(width = 1.dp, color = MaterialTheme.colorScheme.primary),
                contentPadding = PaddingValues(0.dp),
                onClick = {
                    chipsVisibility = !chipsVisibility
                    iconButtonVector = if (chipsVisibility) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown
                }
            ) {
                Icon(
                    imageVector = iconButtonVector,
                    contentDescription = null
                )
            }
        }

        // Toggle chip visibility
        AnimatedVisibility(visible = chipsVisibility) {
            Column {
                Text(
                    modifier = Modifier.padding(
                        vertical = MaterialTheme.dimens.medium,
                        horizontal = MaterialTheme.dimens.medium
                    ),
                    fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                    color = Gray150,
                    text = "Grâce à ces filtres vous pouvez trouver ce que vous cherchez plus facilement"
                )
                FlowRow(
                    modifier = Modifier.padding(start = MaterialTheme.dimens.medium)
                ) {
                    fields.forEachIndexed  { index, field ->
                        // Check if the current chip is selected
                        isSelected = selectedChips.indexOfFirst { it.id == field.id }
                        Log.d("MultiSelectExtended", "any ${selectedChips.any { it.id == field.id }.toString()}")
                        FilterChip(
                            shape = RoundedCornerShape(15.dp),
                            modifier = Modifier.padding(MaterialTheme.dimens.small),
                            selected = isSelected != -1,
                            onClick = {
                                onSelectedItem(field)
                            },
                            label = {
                                Text(
                                    text = field.name,
                                    fontWeight = FontWeight.Bold,
                                )
                            }
                        )
                    }
                }
            }
        }
    }
}
