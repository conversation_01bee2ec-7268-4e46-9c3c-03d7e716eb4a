package se.scmv.morocco.common.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.provider.OpenableColumns
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.io.OutputStream

object IOUtils {
    /**
     * Creates a temporary file for storing camera images.
     *
     * This function creates a uniquely named temporary JPEG file in the app's
     * external pictures directory. The filename is prefixed with "IMG_" followed by
     * the current timestamp to ensure uniqueness.
     *
     * @param context The application context needed to access external storage
     * @return A newly created temporary File object, or null if file creation fails
     */
    fun createCameraImageTempFile(context: Context): File? {
        val timeStamp =
            Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).toString()
        val imageFileName = "IMG_" + timeStamp + "_"
        val storageDir: File? = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            imageFileName,  /* prefix */
            ".jpg",  /* suffix */
            storageDir /* directory */
        )
    }

    /**
     * Extracts the real file path from a content URI.
     *
     * This function converts a content URI to a real file path by:
     * 1. Querying the content resolver for the file name
     * 2. Creating a temporary file in the app's internal storage
     * 3. Copying the content from the URI to this temporary file
     *
     * @param uri The URI from which to extract the file path
     * @param context The application context needed to access the content resolver
     * @return The path to the temporary file containing the URI's content, or null if extraction fails
     */
    fun getRealPathFromURI(uri: Uri, context: Context): String? {
        val returnCursor = context.contentResolver.query(uri, null, null, null, null)
        val nameIndex = returnCursor!!.getColumnIndex(OpenableColumns.DISPLAY_NAME)
        returnCursor.moveToFirst()
        val name = returnCursor.getString(nameIndex)
        val file = File(context.filesDir, name)
        try {
            val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
            val outputStream = FileOutputStream(file)
            var read = 0
            val maxBufferSize = 1 * 1024 * 1024
            val bytesAvailable: Int = inputStream?.available() ?: 0
            //int bufferSize = 1024;
            val bufferSize = bytesAvailable.coerceAtMost(maxBufferSize)
            val buffers = ByteArray(bufferSize)
            while (inputStream?.read(buffers).also {
                    if (it != null) {
                        read = it
                    }
                } != -1) {
                outputStream.write(buffers, 0, read)
            }
            inputStream?.close()
            outputStream.close()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return file.path
    }

    /**
     * Gets the size of a file from a URI.
     *
     * This function attempts to determine the size of a file referenced by a URI using
     * multiple strategies:
     * 1. First tries using the MediaStore query to get the file size
     * 2. If that fails, attempts to use a file descriptor
     * 3. As a last resort, copies the file content to measure the size
     *
     * @param context The application context needed to access the content resolver
     * @param uri The URI of the file whose size is to be determined
     * @return The size of the file in bytes, or -1 if the size cannot be determined
     */
    fun getFileSize(context: Context, uri: Uri): Long {
        return try {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val sizeIndex = cursor.getColumnIndex(MediaStore.MediaColumns.SIZE)
                if (sizeIndex != -1 && cursor.moveToFirst()) {
                    cursor.getLong(sizeIndex)
                } else {
                    // Fallback to file descriptor if cursor method fails
                    context.contentResolver.openFileDescriptor(uri, "r")?.use {
                        it.statSize
                    } ?: -1
                }
            } ?: -1
        } catch (e: Exception) {
            e.printStackTrace()
            // Last resort: use the input stream method (takes more time)
            getSizeFromUri(uri, context)
        }
    }

    private fun getSizeFromUri(uri: Uri, context: Context): Long {
        val returnCursor = context.contentResolver.query(uri, null, null, null, null)
        val nameIndex = returnCursor!!.getColumnIndex(OpenableColumns.DISPLAY_NAME)
        returnCursor.moveToFirst()
        val name = returnCursor.getString(nameIndex)
        val file = File(context.filesDir, name)
        try {
            val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
            val outputStream = FileOutputStream(file)
            var read = 0
            val maxBufferSize = 1 * 1024 * 1024
            val bytesAvailable: Int = inputStream?.available() ?: 0
            //int bufferSize = 1024;
            val bufferSize = bytesAvailable.coerceAtMost(maxBufferSize)
            val buffers = ByteArray(bufferSize)
            while (inputStream?.read(buffers).also {
                    if (it != null) {
                        read = it
                    }
                } != -1) {
                outputStream.write(buffers, 0, read)
            }
            inputStream?.close()
            outputStream.close()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return file.length()
    }

    /**
     * Saves a bitmap as a JPEG file in the device's pictures directory.
     *
     * This function handles the differences between Android Q (API 29) and above versus
     * older Android versions when saving images. For Android Q and above, it uses the MediaStore API
     * to save the file in a way that's compatible with scoped storage. For older versions, it directly
     * saves to the public pictures directory.
     *
     * @param context The application context needed to access the content resolver and storage
     * @param bitmap The bitmap image to be saved
     * @param fileName The name to give to the saved file (without extension for Android Q+)
     * @return The File object representing the saved image, or null if saving failed
     */
    fun saveBitmapInFile(
        context: Context,
        bitmap: Bitmap,
        fileName: String
    ): File? {
        val imageOutStream: OutputStream
        val imageFile: File
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val values = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, fileName)
                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                put(
                    MediaStore.Images.Media.RELATIVE_PATH,
                    Environment.DIRECTORY_PICTURES
                )
            }
            context.contentResolver.run {
                val uri = context.contentResolver?.insert(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    values
                ) ?: return null
                imageFile = File(uri.path, fileName)
                imageOutStream = context.contentResolver?.openOutputStream(uri) ?: return null
            }
        } else {
            val imagePath = Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_PICTURES
            ).absolutePath
            imageFile = File(imagePath, "$fileName.jpeg")
            imageOutStream = FileOutputStream(imageFile)
        }
        imageOutStream.use { bitmap.compress(Bitmap.CompressFormat.JPEG, 100, it) }
        imageOutStream.close()
        return imageFile
    }
}