package se.scmv.morocco.common.extensions

import android.content.Context
import se.scmv.morocco.common.R
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.floor

fun Context.getTimeAgoInFrench(date: String): String {
    val adInsertionInstant = ZonedDateTime.parse(
        date,
        DateTimeFormatter.ISO_DATE_TIME
    ).toInstant()
    val currentTime = Instant.now().atZone(ZoneId.systemDefault()).toInstant()
    val timeDifference = Duration.between(adInsertionInstant, currentTime)
    val yearsPassed = timeDifference.toDays() / 365
    var daysRemaining = timeDifference.toDays() % 365
    val monthsPassed = floor(daysRemaining / 30.44).toLong()
    daysRemaining = (daysRemaining - monthsPassed * 30.44).toLong()
    val weeksPassed = daysRemaining / 7
    daysRemaining %= 7
    val hoursPassed = timeDifference.toHours() % 24
    val minutesPassed = timeDifference.toMinutes() % 60
    val secondsPassed = timeDifference.seconds % 60
    return if (yearsPassed > 0) {
        this.resources.getQuantityString(
            R.plurals.time_passed_years,
            yearsPassed.toInt(),
            yearsPassed.toInt()
        )
    } else if (monthsPassed > 0) {
        this.resources.getQuantityString(
            R.plurals.time_passed_months,
            monthsPassed.toInt(),
            monthsPassed.toInt()
        )
    } else if (weeksPassed > 0) {
        this.resources.getQuantityString(
            R.plurals.time_passed_weeks,
            weeksPassed.toInt(),
            weeksPassed.toInt()
        )
    } else if (daysRemaining > 0) {
        this.resources.getQuantityString(
            R.plurals.time_passed_days,
            daysRemaining.toInt(),
            daysRemaining.toInt()
        )
    } else if (hoursPassed > 0) {
        this.resources.getQuantityString(
            R.plurals.time_passed_hours,
            hoursPassed.toInt(),
            hoursPassed.toInt()
        )
    } else if (minutesPassed > 0) {
        this.resources.getQuantityString(
            R.plurals.time_passed_minutes,
            minutesPassed.toInt(),
            minutesPassed.toInt()
        )
    } else {
        this.resources.getQuantityString(
            R.plurals.time_passed_seconds,
            secondsPassed.toInt(),
            secondsPassed.toInt()
        )
    }
}