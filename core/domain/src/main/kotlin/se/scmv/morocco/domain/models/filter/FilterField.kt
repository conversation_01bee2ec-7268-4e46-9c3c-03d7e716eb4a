package se.scmv.morocco.domain.models.filter

import android.os.Parcel
import android.os.Parcelable


data class ParamsFilter(
    val singleMatch: SingleMatch?,
    val listMatch: ListMatch?,
    val rangeMatch: List<RangeParam>?
)

data class SingleMatch(
    val text: List<SingleTextParam>?,
    val boolean: List<BooleanParam>?,
    val numeric: List<NumericParam>?
)

data class NumericParam(
    val name: String,
    val value: Double
)

data class BooleanParam(
    val name: String,
    val value: Boolean
)

data class ListMatch(
    val textList: List<TextParam>?
)

data class SingleTextParam(
    val name: String,
    val value: String
)

data class TextParam(
    val name: String,
    val value: List<String>?
)

//TODO: replace this with kotlin parcelable annotation
data class RangeParam(val name: String, val value: RangeValue) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString()!!,
        parcel.readParcelable(RangeValue::class.java.classLoader)!!
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(name)
        parcel.writeParcelable(value, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<RangeParam> {
        override fun createFromParcel(parcel: Parcel): RangeParam {
            return RangeParam(parcel)
        }

        override fun newArray(size: Int): Array<RangeParam?> {
            return arrayOfNulls(size)
        }
    }
}

data class RangeValue(val greaterThanOrEqual: Double, val lessThanOrEqual: Double) :
    Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readDouble(),
        parcel.readDouble()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeDouble(greaterThanOrEqual)
        parcel.writeDouble(lessThanOrEqual)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<RangeValue> {
        override fun createFromParcel(parcel: Parcel): RangeValue {
            return RangeValue(parcel)
        }

        override fun newArray(size: Int): Array<RangeValue?> {
            return arrayOfNulls(size)
        }
    }
}
