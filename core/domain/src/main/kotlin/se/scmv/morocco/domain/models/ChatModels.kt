package se.scmv.morocco.domain.models

import java.util.Date

data class Conversation(
    val id: String,
    val partner: PartnerProfile?,
    val ad: Ad?,
    val messages: List<Message>,
    val lastMessage: Message?,
    val isBlockedByMe: Boolean,
    val unreadCount: Int
)

sealed interface Profile {
    val logo: String?
    val name: String
}

data class PrivateProfile(
    override val logo: String?,
    override val name: String
) : Profile

data class StoreProfile(
    override val logo: String?,
    override val name: String,
) : Profile

data class PartnerProfile(
    val logo: String?,
    val name: String
)

data class Ad(
    val adId: String,
    val listId: String,
    val title: String,
    val price: Price,
    val media: Media?
)

data class Price(
    val withCurrency: String?,
    val withoutCurrency: Int?
)

data class Media(
    val defaultImage: DefaultImage?
)

data class DefaultImage(
    val paths: ImagePaths
)

data class ImagePaths(
    val smallThumbnail: String
)

data class Message(
    val id: String,
    val text: String,
    val attachment: Attachment?,
    val isUnread: Boolean,
    val isMine: Boolean,
    val time: Date,
    val conversationId: String? = null
)

data class Attachment(
    val filePath: String,      // Absolute file path for sending
    val previewUri: String,    // FileProvider URI (content://...) for preview
    val type: String           // MIME type
)

data class RealtimeChatEvent(
    val message: Message
)