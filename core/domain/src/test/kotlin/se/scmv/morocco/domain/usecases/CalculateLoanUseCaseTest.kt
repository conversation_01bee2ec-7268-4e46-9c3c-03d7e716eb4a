package se.scmv.morocco.domain.usecases

import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import se.scmv.morocco.domain.models.LoanSimulatorConfig
import se.scmv.morocco.domain.models.LoanSimulatorInput
import se.scmv.morocco.domain.models.LoanSimulatorOutput
import kotlin.test.assertEquals

@RunWith(JUnit4::class)
class CalculateLoanUseCaseTest {

    private val config = LoanSimulatorConfig(
        loanDurations = listOf(7, 12, 25),
        defaultDuration = 25,
        interestPercentage = 4.2,
        redirectionUrl = ""
    )

    private val useCase = CalculateLoanUseCase(config)

    @Test
    fun `goodPrice = 2000000, personalContributionPrice = 0, loanDuration = 25`() {
        // GIVEN
        val input = LoanSimulatorInput(
            goodPrice = 2000000.0,
            personalContributionPrice = 0.0,
            loanDuration = 25
        )

        // WHEN
        val output = useCase(input)

        // THEN
        assertEquals(
            LoanSimulatorOutput(
                monthlyPayment = 10779,
                loanAmount = 2000000.0,
                interestAmount = 1233700,
                personalContributionPercentage = 0.0
            ),
            output
        )
    }


    @Test
    fun `goodPrice = 2000000, personalContributionPrice = 100000, loanDuration = 25`() {
        // GIVEN
        val input = LoanSimulatorInput(
            goodPrice = 2000000.0,
            personalContributionPrice = 100000.0,
            loanDuration = 25
        )

        // WHEN
        val output = useCase(input)

        // THEN
        assertEquals(
            LoanSimulatorOutput(
                monthlyPayment = 10240,
                loanAmount = 1900000.0,
                interestAmount = 1172000,
                personalContributionPercentage = 5.0
            ),
            output
        )
    }

    @Test
    fun `goodPrice = 1050000, personalContributionPrice = 100000, loanDuration = 7`() {
        // GIVEN
        val input = LoanSimulatorInput(
            goodPrice = 2000000.0,
            personalContributionPrice = 100000.0,
            loanDuration = 7
        )

        // WHEN
        val output = useCase(input)

        // THEN
        assertEquals(
            LoanSimulatorOutput(
                monthlyPayment = 26146,
                loanAmount = 1900000.0,
                interestAmount = 296264,
                personalContributionPercentage = 5.0
            ),
            output
        )
    }
}