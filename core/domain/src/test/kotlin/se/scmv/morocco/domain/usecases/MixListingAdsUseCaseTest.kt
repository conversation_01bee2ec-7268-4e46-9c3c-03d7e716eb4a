package se.scmv.morocco.domain.usecases

import junit.framework.TestCase
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import se.scmv.morocco.domain.models.AdParams
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.models.Area
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.ListingAd
import java.util.UUID

@RunWith(JUnit4::class)
class MixListingAdsUseCaseTest {

    private val publishedAd = ListingAd.Published(
        id = UUID.randomUUID().toString(),
        logo = null,
        sellerName = null,
        date = "deserunt",
        isHighlighted = false,
        defaultImage = null,
        imageCount = 1508,
        videoCount = 2015,
        isStore = false,
        isEcommerce = false,
        offersShipping = false,
        isFavorite = false,
        title = "assueverit",
        location = City(
            "110",
            "molestiae",
            trackingName = "molestiae",
            area = Area(id = "110", name = "molestiae", trackingName = "molestiae"),
        ),
        category = Category(
            id = "4845",
            name = "Imogene Conner",
            icon = "",
            trackingName = "Imogene Conner"
        ),
        price = AdPrice.Unavailable,
        discount = null,
        isHotDeal = false,
        isUrgent = false,
        isVerifiedSeller = false,
        params = AdParams(primary = listOf(), secondary = listOf(), extra = listOf()),
        listId = UUID.randomUUID().toString(),
        description = "This is a fake description",
    )

    private val premiumAd = ListingAd.Premium(
        id = UUID.randomUUID().toString(),
        logo = null,
        sellerName = null,
        date = "deserunt",
        defaultImage = null,
        imageCount = 1508,
        videoCount = 2015,
        isStore = false,
        isEcommerce = false,
        offersShipping = false,
        title = "assueverit",
        location = City(
            "110",
            "molestiae",
            trackingName = "molestiae",
            area = Area(id = "110", name = "molestiae", trackingName = "molestiae"),
        ),
        category = Category(
            id = "4845",
            name = "Imogene Conner",
            icon = "",
            trackingName = "Imogene Conner"
        ),
        price = AdPrice.Unavailable,
        discount = null,
        isVerifiedSeller = false,
        params = AdParams(primary = listOf(), secondary = listOf(), extra = listOf()),
        listId = UUID.randomUUID().toString(),
        description = "This is a fake description",
        adType = "assueverit",
        isHotDeal = false,
        isUrgent = false,
        videoUrl = null
    )

    private val dfpBannerAd = ListingAd.DfpBanner(
        uuid = UUID.randomUUID().toString(),
        subject = "assueverit",
        paramsValues = emptyMap()
    )

    @Test
    fun `empty lists`() {
        // GIVEN
        val normalAds = emptyList<ListingAd.Published>()
        val premiumAds = emptyList<ListingAd.Premium>()
        val dfpBannerAds = emptyList<ListingAd.DfpBanner>()

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, dfpBannerAds)

        // THEN
        TestCase.assertTrue(result.isEmpty())
    }

    @Test
    fun `empty premium ads list`() {
        // GIVEN
        val normalAds = List(35) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = emptyList<ListingAd.Premium>()
        val dfpBannerAds = List(2) {
            dfpBannerAd.copy(uuid = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, dfpBannerAds)

        // THEN
        TestCase.assertTrue(result.none { it is ListingAd.Premium })
        TestCase.assertTrue(result.any { it is ListingAd.Published || it is ListingAd.DfpBanner })
        TestCase.assertEquals(result.size, 37)
        for (i in 0..19) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[20] is ListingAd.DfpBanner)
        for (i in 21..35) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[36] is ListingAd.DfpBanner)
    }

    @Test
    fun `empty premium ads list, normal ads size inferior 20`() {
        // GIVEN
        val normalAds = List(16) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = emptyList<ListingAd.Premium>()
        val dfpBannerAds = List(2) {
            dfpBannerAd.copy(uuid = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, dfpBannerAds)

        // THEN
        TestCase.assertTrue(result.none { it is ListingAd.Premium })
        TestCase.assertTrue(result.any { it is ListingAd.Published || it is ListingAd.DfpBanner })
        TestCase.assertEquals(result.size, 18)
        for (i in 0..15) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[16] is ListingAd.DfpBanner)
        TestCase.assertTrue(result[17] is ListingAd.DfpBanner)
    }

    @Test
    fun `empty normal ads list, only one premium ad`() {
        // GIVEN
        val normalAds = emptyList<ListingAd.Published>()
        val premiumAds = listOf(premiumAd)
        val dfpBannerAds = List(2) {
            dfpBannerAd.copy(uuid = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, dfpBannerAds)

        // THEN
        TestCase.assertTrue(result.none { it is ListingAd.Published })
        TestCase.assertTrue(result.any { it is ListingAd.Premium || it is ListingAd.DfpBanner })
        TestCase.assertTrue(result.size == 5)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        TestCase.assertTrue(result[1] is ListingAd.Premium)
        TestCase.assertTrue(result[2] is ListingAd.Premium)
        TestCase.assertTrue(result[3] is ListingAd.DfpBanner)
        TestCase.assertTrue(result[4] is ListingAd.DfpBanner)
    }

    @Test
    fun `empty normal ads list, 3 premium ads`() {
        // GIVEN
        val normalAds = emptyList<ListingAd.Published>()
        val premiumAds = List(3) { premiumAd }
        val dfpBannerAds = List(2) {
            dfpBannerAd.copy(uuid = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, dfpBannerAds)

        // THEN
        TestCase.assertTrue(result.none { it is ListingAd.Published })
        TestCase.assertTrue(result.any { it is ListingAd.Premium || it is ListingAd.DfpBanner })
        TestCase.assertEquals(result.size, 5)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        TestCase.assertTrue(result[1] is ListingAd.Premium)
        TestCase.assertTrue(result[2] is ListingAd.Premium)
        TestCase.assertTrue(result[3] is ListingAd.DfpBanner)
        TestCase.assertTrue(result[4] is ListingAd.DfpBanner)
    }

    @Test
    fun `3 premium ads, 35 normal ads and 0 dfpBanner ads`() {
        // GIVEN
        val normalAds = List(35) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = List(3) {
            premiumAd.copy(id = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, emptyList())

        // THEN
        TestCase.assertTrue(result.isNotEmpty())
        TestCase.assertEquals(result.size, 38)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Published>().size == 35)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Premium>().size == 3)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        for (i in 1..9) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[11] is ListingAd.Premium)
        for (i in 12..21) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[22] is ListingAd.Premium)
        for (i in 23..34) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[0] != result[11] && result[11] != result[22] && result[0] != result[22])
    }

    @Test
    fun `2 premium ads, 35 normal ads and 0 dfpBanner ads`() {
        // GIVEN
        val normalAds = List(35) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = List(2) {
            premiumAd.copy(id = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, emptyList())

        // THEN
        TestCase.assertTrue(result.isNotEmpty())
        TestCase.assertEquals(result.size, 38)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Published>().size == 35)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Premium>().size == 3)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        for (i in 1..9) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[11] is ListingAd.Premium)
        for (i in 12..21) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[22] is ListingAd.Premium)
        for (i in 23..34) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[0] != result[11] && result[11] != result[22] && result[0] == result[22])
    }

    @Test
    fun `1 premium ads, 35 normal ads and 0 dfpBanner ads`() {
        // GIVEN
        val normalAds = List(35) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = List(1) {
            premiumAd.copy(id = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, emptyList())

        // THEN
        TestCase.assertTrue(result.isNotEmpty())
        TestCase.assertEquals(result.size, 38)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Published>().size == 35)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Premium>().size == 3)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        for (i in 1..9) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[11] is ListingAd.Premium)
        for (i in 12..21) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[22] is ListingAd.Premium)
        for (i in 23..34) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[0] == result[11] && result[11] == result[22] && result[0] == result[22])
    }

    @Test
    fun `injects 3 premium ads in a list of 10 normal ads, and 0 dfp banner ads`() {
        // GIVEN
        val normalAds = List(10) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = List(3) {
            premiumAd.copy(id = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, emptyList())

        // THEN
        TestCase.assertTrue(result.isNotEmpty())
        TestCase.assertEquals(result.size, 13)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Published>().size == 10)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Premium>().size == 3)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        for (i in 1..10) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[11] is ListingAd.Premium)
        TestCase.assertTrue(result[12] is ListingAd.Premium)
        TestCase.assertTrue(result[0] != result[11] && result[11] != result[12] && result[11] != result[12])
    }

    @Test
    fun `injects 2 premium ads in a list of 5 normal ads, and 0 dfp banner ads`() {
        // GIVEN
        val normalAds = List(5) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = List(2) {
            premiumAd.copy(id = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, emptyList())

        // THEN
        TestCase.assertTrue(result.isNotEmpty())
        TestCase.assertEquals(result.size, 8)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Published>().size == 5)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Premium>().size == 3)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        for (i in 1..5) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[6] is ListingAd.Premium)
        TestCase.assertTrue(result[7] is ListingAd.Premium)
        TestCase.assertTrue(result[0] != result[6] && result[6] != result[7] && result[0] == result[7])
    }

    @Test
    fun `3 premium ads, 35 normal ads and 2 dfpBanner ads`() {
        // GIVEN
        val normalAds = List(35) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = List(3) {
            premiumAd.copy(id = UUID.randomUUID().toString())
        }
        val dfpBannerAds = List(2) {
            dfpBannerAd.copy(uuid = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, dfpBannerAds)

        // THEN
        TestCase.assertTrue(result.isNotEmpty())
        TestCase.assertEquals(result.size, 40)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Published>().size == 35)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Premium>().size == 3)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        for (i in 1..9) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[11] is ListingAd.Premium)
        for (i in 12..21) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[22] is ListingAd.Premium)
        TestCase.assertTrue(result[23] is ListingAd.DfpBanner)
        for (i in 24..38) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[39] is ListingAd.DfpBanner)
        TestCase.assertTrue(result[0] != result[11] && result[11] != result[22] && result[0] != result[22])
    }

    @Test
    fun `1 premium ads, 35 normal ads and 2 dfpBanner ads`() {
        // GIVEN
        val normalAds = List(35) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = List(1) {
            premiumAd.copy(id = UUID.randomUUID().toString())
        }
        val dfpBannerAds = List(2) {
            dfpBannerAd.copy(uuid = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, dfpBannerAds)

        // THEN
        TestCase.assertTrue(result.isNotEmpty())
        TestCase.assertEquals(result.size, 40)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Published>().size == 35)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Premium>().size == 3)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        for (i in 1..9) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[11] is ListingAd.Premium)
        for (i in 12..21) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[22] is ListingAd.Premium)
        TestCase.assertTrue(result[23] is ListingAd.DfpBanner)
        for (i in 24..38) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[39] is ListingAd.DfpBanner)
        TestCase.assertTrue(result[0] == result[11] && result[11] == result[22] && result[0] == result[22])
    }

    @Test
    fun `injects 3 premium ads in a list of 10 normal ads, and 2 dfp banner ads`() {
        // GIVEN
        val normalAds = List(10) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = List(3) {
            premiumAd.copy(id = UUID.randomUUID().toString())
        }
        val dfpBannerAds = List(2) {
            dfpBannerAd.copy(uuid = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, dfpBannerAds)

        // THEN
        TestCase.assertTrue(result.isNotEmpty())
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Published>().size == 10)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Premium>().size == 3)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.DfpBanner>().size == 2)
        TestCase.assertTrue(result[0] is ListingAd.Premium)
        TestCase.assertEquals(result.size, 15)
        for (i in 1..10) {
            TestCase.assertTrue(result[i] is ListingAd.Published)
        }
        TestCase.assertTrue(result[11] is ListingAd.Premium)
        TestCase.assertTrue(result[12] is ListingAd.Premium)
        TestCase.assertTrue(result[13] is ListingAd.DfpBanner)
        TestCase.assertTrue(result[14] is ListingAd.DfpBanner)
        TestCase.assertTrue(result[0] != result[11] && result[11] != result[12] && result[11] != result[12])
    }

    @Test
    fun `0 premium ads, 1 normal ads, and 2 dfp banner ads`() {
        // GIVEN
        val normalAds = List(1) {
            publishedAd.copy(id = UUID.randomUUID().toString())
        }
        val premiumAds = emptyList<ListingAd.Premium>()
        val dfpBannerAds = List(2) {
            dfpBannerAd.copy(uuid = UUID.randomUUID().toString())
        }

        // WHEN
        val result = MixListingAdsUseCase.invoke(normalAds, premiumAds, dfpBannerAds)

        // THEN
        TestCase.assertTrue(result.isNotEmpty())
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Published>().size == 1)
        TestCase.assertTrue(result.filterIsInstance<ListingAd.Premium>().isEmpty())
        TestCase.assertTrue(result.filterIsInstance<ListingAd.DfpBanner>().size == 2)
        TestCase.assertEquals(result.size, 3)
        TestCase.assertTrue(result[0] is ListingAd.Published)
        TestCase.assertTrue(result[1] is ListingAd.DfpBanner)
        TestCase.assertTrue(result[2] is ListingAd.DfpBanner)
    }
}