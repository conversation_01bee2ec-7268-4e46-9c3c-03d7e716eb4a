package se.scmv.morocco.data.database

import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import se.scmv.morocco.data.database.daos.CityDao
import se.scmv.morocco.data.database.daos.RecentSearchDao
import se.scmv.morocco.data.database.daos.TownDao
import se.scmv.morocco.data.database.entities.CityEntity
import se.scmv.morocco.data.database.entities.RecentSearchEntity
import se.scmv.morocco.data.database.entities.TownEntity

@TypeConverters()
@Database(
    entities = [CityEntity::class, TownEntity::class, RecentSearchEntity::class],
    version = 5,
    exportSchema = true,
    autoMigrations = [
        AutoMigration(from = 4, to = 5)
    ]
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun cityDao(): CityDao
    abstract fun townDao(): TownDao
    abstract fun recentSearchDao(): RecentSearchDao
}


