package se.scmv.morocco.data.rest.config.dtos

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class LoanSimulatorConfigDto(
    @SerializedName("loan_durations") val loanDurations: List<Int>,
    @SerializedName("default_duration") val defaultDuration: Int,
    @SerializedName("interest_percentage") val interestPercentage: Double,
    @SerializedName("loan_categories") val loanCategories: List<LoanCategory>,
    @SerializedName("redirection_url") val redirectionUrl: String
)

@Keep
data class LoanCategory(
    @SerializedName("category") val category: String,
    @SerializedName("type") val type: String
)
