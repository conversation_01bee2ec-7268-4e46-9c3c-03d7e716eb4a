package se.scmv.morocco.data.repository

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.DefaultUpload
import com.apollographql.apollo3.api.Optional
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import se.scmv.morocco.BlockConversationMutation
import se.scmv.morocco.ClearConversationMutation
import se.scmv.morocco.GetChatConversationByIdQuery
import se.scmv.morocco.GetConversationsListQuery
import se.scmv.morocco.GetUnreadCountQuery
import se.scmv.morocco.InboxChattingSubscription
import se.scmv.morocco.MakeConversationAsReadMutation
import se.scmv.morocco.SendChatMessageMutation
import se.scmv.morocco.UnblockConversationMutation
import se.scmv.morocco.data.di.WebSocketApolloClient
import se.scmv.morocco.data.repository.messaging.ConversationPagingSource
import se.scmv.morocco.data.repository.messaging.MessagePagingSource
import se.scmv.morocco.data.session.SessionManager
import se.scmv.morocco.domain.models.Ad
import se.scmv.morocco.domain.models.Attachment
import se.scmv.morocco.domain.models.Conversation
import se.scmv.morocco.domain.models.DefaultImage
import se.scmv.morocco.domain.models.ImagePaths
import se.scmv.morocco.domain.models.Media
import se.scmv.morocco.domain.models.Message
import se.scmv.morocco.domain.models.PartnerProfile
import se.scmv.morocco.domain.models.Price
import se.scmv.morocco.domain.models.RealtimeChatEvent
import se.scmv.morocco.domain.repositories.ChatRepository
import se.scmv.morocco.type.SendChatMessageInput
import java.io.File
import java.time.Instant
import java.util.Date
import javax.inject.Inject

class ChatRepositoryImpl @Inject constructor(
    private val apolloClient: ApolloClient,
    @WebSocketApolloClient private val webSocketApolloClient: ApolloClient,
    private val sessionManager: SessionManager
) : ChatRepository {

    private fun parseDate(dateString: String): Date {
        return try {
            // Parse ISO 8601 date string to Instant and then to Date
            Date.from(Instant.parse(dateString))
        } catch (e: Exception) {
            Date() // Fallback to current date if parsing fails
        }
    }

    // Paging3 methods
    override fun getConversationsPaging(): Flow<PagingData<Conversation>> {
        return Pager<Long, Conversation>(
            config = PagingConfig(
                pageSize = ConversationPagingSource.Companion.PAGE_SIZE,
                enablePlaceholders = false,
                prefetchDistance = 5
            ),
            pagingSourceFactory = { ConversationPagingSource(apolloClient) }
        ).flow
    }

    override fun getMessagesPaging(conversationId: String): Flow<PagingData<Message>> {
        return Pager<Long, Message>(
            config = PagingConfig(
                pageSize = MessagePagingSource.Companion.PAGE_SIZE,
                enablePlaceholders = false,
                prefetchDistance = 2 // Reduced for faster refreshes
            ),
            pagingSourceFactory = { MessagePagingSource(apolloClient, conversationId) }
        ).flow
    }

    override suspend fun getUnreadCount(): Flow<Result<Int>> {
        return apolloClient.query(GetUnreadCountQuery())
            .toFlow()
            .map { response ->
                if (response.hasErrors()) {
                    Result.failure(
                        Exception(
                            response.errors?.firstOrNull()?.message ?: "Unknown error"
                        )
                    )
                } else {
                    Result.success(
                        response.data?.getMyChat?.unreadCount ?: 0
                    )
                }
            }
            .catch { e -> emit(Result.failure(e)) }
    }

    override suspend fun getChatConversation(
        id: String,
        pageSize: Int,
        beforeTime: Date?,
        afterTime: Date?
    ): Flow<Result<Conversation>> {
        return apolloClient.query(
            GetChatConversationByIdQuery(
                id = id,
                size = pageSize,
                beforeTime = beforeTime?.let { Instant.ofEpochMilli(it.time).toString() }
                    ?.let { Optional.Companion.present(it) } ?: Optional.Companion.absent(),
                afterTime = afterTime?.let { Instant.ofEpochMilli(it.time).toString() }
                    ?.let { Optional.Companion.present(it) } ?: Optional.Companion.absent()
            )
        )
            .toFlow()
            .map { response ->
                if (response.hasErrors()) {
                    Result.failure(
                        Exception(
                            response.errors?.firstOrNull()?.message ?: "Unknown error"
                        )
                    )
                } else {
                    Result.success(
                        response.data?.getMyChatConversation.toConversation()
                    )
                }
            }
            .catch { e -> emit(Result.failure(e)) }
    }


    override suspend fun sendMessage(
        conversationId: String,
        text: String,
        attachment: Attachment?
    ): Flow<Result<Message>> {
        return apolloClient.mutation(
            SendChatMessageMutation(
                message = SendChatMessageInput(
                    conversationId = Optional.Companion.present(conversationId),
                    text = Optional.Companion.present(text),
                    attachment = attachment?.let {
                        val attachmentFile = File(it.filePath)
                        val attachmentContentType = if (it.type.contains("image")) {
                            "image/${attachmentFile.extension}"
                        } else if (it.filePath.isNotEmpty()) {
                            if (attachmentFile.extension == "txt") {
                                "text/plain"
                            } else {
                                "application/${attachmentFile.extension}"
                            }
                        } else "application/octet-stream"

                        Optional.Companion.present(
                            DefaultUpload.Builder()
                                .content(attachmentFile.readBytes())
                                .contentType(attachmentContentType)
                                .fileName(attachmentFile.name)
                                .build()
                        )
                    } ?: Optional.Companion.absent()
                )
            )
        )
            .toFlow()
            .map { response ->
                if (response.hasErrors()) {
                    Result.failure(
                        Exception(
                            response.errors?.firstOrNull()?.message ?: "Unknown error"
                        )
                    )
                } else {
                    val sendChatMessage = response.data?.sendChatMessage
                    if (sendChatMessage?.success == true) {
                        val message = sendChatMessage.conversation.message
                        Result.success(message.toMessage())
                    } else {
                        Result.failure(Exception("Failed to send message"))
                    }
                }
            }
            .catch { e -> emit(Result.failure<Message>(e)) }
    }

    private fun SendChatMessageMutation.Message.toMessage(): Message {
        return Message(
            id = id,
            text = text.orEmpty(),
            attachment = attachment?.let {
                Attachment(
                    filePath = it.url,
                    previewUri = it.url,
                    type = it.type
                )
            },
            isUnread = false,
            isMine = true,
            time = parseDate(time)
        )
    }

    override suspend fun getMessages(
        conversationId: String,
        pageSize: Int,
        beforeTime: Date?,
        afterTime: Date?
    ): Flow<Result<List<Message>>> {
        return apolloClient.query(
            GetChatConversationByIdQuery(
                id = conversationId,
                size = pageSize,
                beforeTime = beforeTime?.let { Instant.ofEpochMilli(it.time).toString() }
                    ?.let { Optional.Companion.present(it) } ?: Optional.Companion.absent(),
                afterTime = afterTime?.let { Instant.ofEpochMilli(it.time).toString() }
                    ?.let { Optional.Companion.present(it) } ?: Optional.Companion.absent()
            )
        )
            .toFlow()
            .map { response ->
                if (response.hasErrors()) {
                    Result.failure(
                        Exception(
                            response.errors?.firstOrNull()?.message ?: "Unknown error"
                        )
                    )
                } else {
                    Result.success(
                        response.data?.getMyChatConversation?.messages?.mapNotNull { it?.toMessage() }
                            ?: throw Exception("No messages received")
                    )
                }
            }
            .catch { e -> emit(Result.failure(e)) }
    }


    override suspend fun blockConversation(id: String): Flow<Result<Boolean>> {
        return apolloClient.mutation(BlockConversationMutation(id))
            .toFlow()
            .map { response ->
                if (response.hasErrors()) {
                    Result.failure(
                        Exception(
                            response.errors?.firstOrNull()?.message ?: "Unknown error"
                        )
                    )
                } else {
                    Result.success(response.data?.blockConversation?.success ?: false)
                }
            }
            .catch { e -> emit(Result.failure<Boolean>(e)) }
    }

    override suspend fun unblockConversation(id: String): Flow<Result<Boolean>> {
        return apolloClient.mutation(UnblockConversationMutation(id))
            .toFlow()
            .map { response ->
                if (response.hasErrors()) {
                    Result.failure(
                        Exception(
                            response.errors?.firstOrNull()?.message ?: "Unknown error"
                        )
                    )
                } else {
                    Result.success(response.data?.unblockConversation?.success ?: false)
                }
            }
            .catch { e -> emit(Result.failure(e)) }

    }

    override suspend fun clearConversation(id: String): Flow<Result<Boolean>> {
        return apolloClient.mutation(ClearConversationMutation(id))
            .toFlow()
            .map { response ->
                if (response.hasErrors()) {
                    Result.failure(
                        Exception(
                            response.errors?.firstOrNull()?.message ?: "Unknown error"
                        )
                    )
                } else {
                    Result.success(response.data?.clearConversation?.success ?: false)
                }
            }
            .catch { e -> emit(Result.failure(e)) }

    }

    override suspend fun markConversationAsRead(id: String): Flow<Result<Boolean>> {
        return apolloClient.mutation(MakeConversationAsReadMutation(id))
            .toFlow()
            .map { response ->
                if (response.hasErrors()) {
                    Result.failure(
                        Exception(
                            response.errors?.firstOrNull()?.message ?: "Unknown error"
                        )
                    )
                } else {
                    Result.success(response.data?.markConversationAsRead?.success ?: false)
                }
            }
            .catch { e -> emit(Result.failure(e)) }
    }


    override suspend fun subscribeToChat(): Flow<RealtimeChatEvent> {
        val token = sessionManager.getAccessToken()
        if (token.isNullOrBlank()) {
            throw Exception("No authentication token available")
        }
        return webSocketApolloClient.subscription(InboxChattingSubscription(token = token))
            .toFlow()
            .catch { e ->
                // Don't wrap the exception - preserve the original type for proper error handling
                throw e
            }
            .mapNotNull { event ->
                val eventData = event.data?.subscribe?.event
                if (eventData == null) {
                    return@mapNotNull null
                }
                val message = eventData.onMessageReceived
                if (message == null) {
                    return@mapNotNull null
                }
                val parsedMessage = message.toMessage()
                if (parsedMessage == null) {
                    return@mapNotNull null
                }
                RealtimeChatEvent(message = parsedMessage)
            }
    }

    // Extension functions to convert GraphQL types to domain models

    private fun GetConversationsListQuery.Conversation?.toConversation(): Conversation {
        return Conversation(
            id = this?.id.orEmpty(),
            partner = PartnerProfile(
                name = if (this?.partner?.__typename == "StoreProfile") this?.partner!!.onStoreProfile?.name.toString() else this?.partner?.onPrivateProfile?.name.toString(),
                logo = if (this?.partner?.__typename == "StoreProfile") this?.partner!!.onStoreProfile?.logo?.defaultPath.orEmpty() else null
            ),
            ad = this?.ad?.toAd(),
            lastMessage = this?.lastMessage?.toMessage(),
            isBlockedByMe = this?.isBlockedByMe ?: false,
            unreadCount = this?.unreadCount ?: 0,
            messages = emptyList()
        )
    }


    private fun GetConversationsListQuery.Ad.toAd(): Ad {
        return Ad(
            adId = adId,
            listId = listId,
            title = title,
            price = Price(
                withCurrency = price?.withCurrency,
                withoutCurrency = price?.withoutCurrency
            ),
            media = media?.let {
                Media(
                    defaultImage = it.defaultImage?.let { image ->
                        DefaultImage(
                            paths = ImagePaths(
                                smallThumbnail = image.paths.smallThumbnail
                            )
                        )
                    }
                )
            }
        )
    }

    private fun GetConversationsListQuery.LastMessage.toMessage(): Message {
        return Message(
            id = id,
            text = text.orEmpty(),
            attachment = attachment?.let {
                Attachment(
                    filePath = it.url,
                    previewUri = it.url,
                    type = it.type
                )
            },
            isUnread = isUnread,
            isMine = isMine,
            time = parseDate(time),
        )
    }

    private fun InboxChattingSubscription.OnMessageReceived?.toMessage(): Message? {
        return Message(
            id = this?.id.orEmpty(),
            text = this?.text.orEmpty(),
            attachment = this?.attachment?.let {
                Attachment(
                    filePath = it.url,
                    previewUri = it.url,
                    type = it.type
                )
            },
            isUnread = true,
            isMine = false,
            time = parseDate(this!!.time),
            conversationId = this?.conversationId

        )
    }

    private fun GetChatConversationByIdQuery.GetMyChatConversation?.toConversation(): Conversation {
        return Conversation(
            id = this?.id.orEmpty(),
            partner = PartnerProfile(
                name = if (this?.partner?.__typename == "StoreProfile") this?.partner!!.onStoreProfile?.name.toString() else this?.partner?.onPrivateProfile?.name.toString(),
                logo = if (this?.partner?.__typename == "StoreProfile") this?.partner!!.onStoreProfile?.logo?.defaultPath.orEmpty() else null
            ),
            ad = this?.ad?.toAd(),
            lastMessage = this?.messages?.lastOrNull()?.toMessage(),
            isBlockedByMe = this?.isBlockedByMe ?: false,
            messages = this?.messages?.mapNotNull { it?.toMessage() } ?: emptyList(),
            unreadCount = this?.unreadCount ?: 0
        )
    }


    private fun GetChatConversationByIdQuery.Ad?.toAd(): Ad? {
        return Ad(
            adId = this?.adId.orEmpty(),
            listId = this?.listId.orEmpty(),
            title = this?.title.orEmpty(),
            price = Price(
                withCurrency = this?.price?.withCurrency,
                withoutCurrency = this?.price?.withoutCurrency
            ),
            media = this?.media?.let {
                Media(
                    defaultImage = it.defaultImage?.let { image ->
                        DefaultImage(
                            paths = ImagePaths(
                                smallThumbnail = image.paths.smallThumbnail
                            )
                        )
                    }
                )
            }
        )
    }

    private fun GetChatConversationByIdQuery.Message?.toMessage(): Message? {
        return Message(
            id = this?.id.orEmpty(),
            text = this?.text.orEmpty(),
            attachment = this?.attachment?.let {
                Attachment(
                    filePath = it.url,
                    previewUri = it.url,
                    type = it.type
                )
            },
            isUnread = this!!.isUnread,
            isMine = this!!.isMine,
            time = parseDate(this!!.time),
        )
    }

}