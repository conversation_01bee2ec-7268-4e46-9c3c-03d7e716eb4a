package se.scmv.morocco.data.di

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.data.repository.AdViewRepositoryImpl
import se.scmv.morocco.data.repository.AuthenticationRepositoryImpl
import se.scmv.morocco.data.repository.ChatRepositoryImpl
import se.scmv.morocco.data.repository.ConfigRepositoryImpl
import se.scmv.morocco.data.repository.ListingAdRepositoryImpl
import se.scmv.morocco.data.repository.RemoteConfigRepositoryFirebaseImpl
import se.scmv.morocco.data.repository.account.AccountAdsRepositoryMyImpl
import se.scmv.morocco.data.repository.account.AccountRepositoryImpl
import se.scmv.morocco.data.repository.ad.AdRepositoryImpl
import se.scmv.morocco.domain.repositories.AccountAdsRepository
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.AdViewRepository
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.domain.repositories.ChatRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.domain.repositories.ListingAdRepository
import se.scmv.morocco.domain.repositories.RemoteConfigRepository
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
abstract class RepositoryModule {

    @Binds
    abstract fun bindAuthenticationRepository(
        authenticationRepositoryImpl: AuthenticationRepositoryImpl
    ): AuthenticationRepository

    @Binds
    abstract fun bindConfigRepository(configRepositoryImpl: ConfigRepositoryImpl): ConfigRepository

    @Binds
    abstract fun bindRemoteConfigRepository(
        remoteConfigRepositoryFirebaseImpl: RemoteConfigRepositoryFirebaseImpl
    ): RemoteConfigRepository

    @Binds
    @Singleton
    abstract fun bindAccountRepository(accountRepository: AccountRepositoryImpl): AccountRepository

    @Binds
    abstract fun bindListingAdRepository(
        listingAdRepositoryImpl: ListingAdRepositoryImpl
    ): ListingAdRepository

    @Binds
    @Singleton
    abstract fun bindMyAdsListingRepository(adsListingRepository: AccountAdsRepositoryMyImpl): AccountAdsRepository

    @Binds
    @Singleton
    abstract fun bindAdViewRepository(adViewRepository: AdViewRepositoryImpl): AdViewRepository

    @Binds
    @Singleton
    abstract fun bindAdRepository(adRepository: AdRepositoryImpl): AdRepository

    @Binds
    @Singleton
    abstract fun bindChatRepository(chatRepositoryImpl: ChatRepositoryImpl ): ChatRepository
}