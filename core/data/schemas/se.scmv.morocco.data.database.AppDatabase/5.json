{"formatVersion": 1, "database": {"version": 5, "identityHash": "62e82124dcc00da1b0a1c82f769f9d96", "entities": [{"tableName": "cities", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name_Fr` TEXT NOT NULL, `name_ar` TEXT NOT NULL, `tracking_name` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameFr", "columnName": "name_Fr", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameAr", "columnName": "name_ar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "trackingName", "columnName": "tracking_name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "towns", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name_ar` TEXT NOT NULL, `name_Fr` TEXT NOT NULL, `tracking_name` TEXT NOT NULL, `city_id` TEXT NOT NULL, PRIMARY KEY(`id`, `city_id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameAr", "columnName": "name_ar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameFr", "columnName": "name_Fr", "affinity": "TEXT", "notNull": true}, {"fieldPath": "trackingName", "columnName": "tracking_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "cityId", "columnName": "city_id", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id", "city_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "recent_searches", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uuid` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `keyword` TEXT, `category_id` TEXT, `category_name` TEXT, `category_tracking_name` TEXT, `ad_type_key` TEXT, `ad_type_name` TEXT, `city_id` TEXT, `city_name` TEXT, `city_tracking_name` TEXT, `model_key` TEXT, `model_id` TEXT, `model_name` TEXT, `brand_key` TEXT, `brand_id` TEXT, `brand_name` TEXT, PRIMARY KEY(`uuid`))", "fields": [{"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "keyword", "columnName": "keyword", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categoryId", "columnName": "category_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categoryName", "columnName": "category_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categoryTrackingName", "columnName": "category_tracking_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "adTypeKey", "columnName": "ad_type_key", "affinity": "TEXT", "notNull": false}, {"fieldPath": "adTypeName", "columnName": "ad_type_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "cityId", "columnName": "city_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "cityName", "columnName": "city_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "cityTrackingName", "columnName": "city_tracking_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "model_key", "affinity": "TEXT", "notNull": false}, {"fieldPath": "modelId", "columnName": "model_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "modelName", "columnName": "model_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "brand<PERSON>ey", "columnName": "brand_key", "affinity": "TEXT", "notNull": false}, {"fieldPath": "brandId", "columnName": "brand_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "brandName", "columnName": "brand_name", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["uuid"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '62e82124dcc00da1b0a1c82f769f9d96')"]}}