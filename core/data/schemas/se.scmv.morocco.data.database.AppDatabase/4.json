{"formatVersion": 1, "database": {"version": 4, "identityHash": "40ea6c26dc9823496b21353405c05832", "entities": [{"tableName": "cities", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name_Fr` TEXT NOT NULL, `name_ar` TEXT NOT NULL, `tracking_name` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameFr", "columnName": "name_Fr", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameAr", "columnName": "name_ar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "trackingName", "columnName": "tracking_name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "towns", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name_ar` TEXT NOT NULL, `name_Fr` TEXT NOT NULL, `tracking_name` TEXT NOT NULL, `city_id` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameAr", "columnName": "name_ar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameFr", "columnName": "name_Fr", "affinity": "TEXT", "notNull": true}, {"fieldPath": "trackingName", "columnName": "tracking_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "cityId", "columnName": "city_id", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '40ea6c26dc9823496b21353405c05832')"]}}