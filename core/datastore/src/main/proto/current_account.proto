syntax = "proto2";

package se.scmv.morocco.datastore;

message PbAccount {
  oneof account_type {
    PbNotConnectedAccount notConnected = 1;
    PbConnectedAccount connected = 2;
  }
}

message PbNotConnectedAccount {}

message PbConnectedAccount {
  oneof connected_type {
    PbPrivateAccount privateAccount = 1;
    PbShopAccount shopAccount = 2;
  }
}

message PbPrivateAccount {
  required PbAccountInfo contact = 1;
  required bool isPhoneHidden = 2;
}

message PbShopAccount {
  required PbAccountInfo contact = 1;
  required PbStoreInfo store = 2;
}

message PbAccountInfo {
  required string accountId = 1;
  required string name = 2;
  required string email = 3;
  optional string phone = 4;
  optional PbCity location = 5;
  required string creationDate = 6;
}

message PbStoreInfo {
  required int32 points = 1;
  required string membership = 2;
  required PbCategory category = 3;
  optional string website = 4;
  required bool verified = 5;
  optional string shortDescription = 6;
  optional string longDescription = 7;
  reserved 8, 9; // Mark removed phone2 and phone3 as reserved, This would maintain backward compatibility with existing data.
  required PbAllowedAccess allowedAccess = 10;
  optional string logoUrl = 11;
  repeated PbCity cities = 12;
  optional string pointsExpirationDate = 13;
  optional string startDate = 14;
  optional string expirationDate = 15;
  repeated string phones = 16;
}

message PbAllowedAccess {
  required bool adHotdealAllowed = 1;
  required int32 adMaxImages = 2;
  required int32 adMaxVideos = 3;
  required bool adUrgentAllowed = 4;
  required bool adsBoostedFilterAllowed = 5;
  required bool adsBulkDeleteAllowed = 6;
  required bool avitoTokenAllowed = 7;
  required bool deliveryAllowed = 8;
  required bool statsPerAdAllowed = 9;
  required bool supportViaWhatsappAllowed = 10;
  required bool vasConfigureExecTimeAllowed = 11;
}

message PbCity {
  required string id = 1;
  required string name = 2;
  required string trackingName = 3;
  optional string address = 4;
}

message PbCategory {
  required string id = 1;
  required string name = 2;
  required string trackingName = 3;
}