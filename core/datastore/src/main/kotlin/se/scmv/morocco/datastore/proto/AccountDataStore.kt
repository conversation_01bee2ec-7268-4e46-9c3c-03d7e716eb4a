package se.scmv.morocco.datastore.proto

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.core.DataStoreFactory
import androidx.datastore.core.okio.OkioStorage
import okio.FileSystem
import okio.Path
import okio.Path.Companion.toPath
import se.scmv.morocco.datastore.PbAccount

internal const val DATA_STORE_FILE_NAME = "account_proto_datastore.pb"

fun getAccountDataStore(context: Context): DataStore<PbAccount> {
    val producePath = { context.filesDir.resolve(DATA_STORE_FILE_NAME).absolutePath.toPath() }

    return createDataStore(fileSystem = FileSystem.SYSTEM, producePath = producePath)
}

fun createDataStore(
    fileSystem: FileSystem,
    producePath: () -> Path
): DataStore<PbAccount> {
    return DataStoreFactory.create(
        storage = OkioStorage(
            fileSystem = fileSystem,
            producePath = producePath,
            serializer = AccountSerializer,
        ),
        migrations = listOf(),
    )
}
