plugins {
    id("avito.android.library")
    id("avito.android.hilt")
    alias(libs.plugins.wire)
}

android {
    namespace = "se.scmv.morocco.datastore"
}

dependencies {
    implementation(project(":core:domain"))

    // Datastore
    api(libs.androidx.datastore)
    implementation(libs.androidx.datastore.preferences)
}

wire {
    kotlin {}
    sourcePath {
        srcDir("src/main/proto")
    }
}

android {
    sourceSets {
        getByName("debug") {
            kotlin.srcDir("build/generated/source/wire/debug")
        }
        getByName("release") {
            kotlin.srcDir("build/generated/source/wire/release")
        }
    }
}

// Ensure Wire generates sources before KSP
tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
    dependsOn("generateProtos")
}

// If using KSP, make sure it runs after Wire
tasks.matching { it.name.contains("ksp") }.configureEach {
    dependsOn("generateProtos")
}