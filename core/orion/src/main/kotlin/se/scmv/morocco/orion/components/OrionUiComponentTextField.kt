package se.scmv.morocco.orion.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.components.avOutlinedTextFieldColors
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionTextField
import java.util.regex.Pattern

class OrionUiComponentTextField(
    private val uiConfig: OrionTextField,
    private val hasNextTextFieldItem: Boolean,
    private val listener: OptionalItemsListener,
    initialValue: OrionKeyStringValue? = null,
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private var state by mutableStateOf((initialValue?.value ?: uiConfig.potentialValue).orEmpty())

    @Composable
    override fun Content(modifier: Modifier) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig.baseData) { Title(text = title, required = required) }
            AvTextField(
                modifier = modifier,
                value = state,
                onValueChanged = { newText ->
                    state = newText
                    error = null
                    listener.onValueChanged(uiConfig, collectValue())
                },
                enabled = uiConfig.enabled,
                keyboardOptions = KeyboardOptions(
                    keyboardType = when (uiConfig.inputType) {
                        OrionTextField.InputType.TEXT -> KeyboardType.Unspecified
                        OrionTextField.InputType.NUMBER -> KeyboardType.Number
                        OrionTextField.InputType.PHONE -> KeyboardType.Phone
                    },
                    imeAction = when {
                        uiConfig.isLarge -> ImeAction.Default
                        else -> if (hasNextTextFieldItem) ImeAction.Next else ImeAction.Done
                    }
                ),
                leadingIcon = { Icon(url = uiConfig.baseData.iconUrl) },
                trailingIcon = uiConfig.suffix?.let {
                    {
                        Text(text = it, style = MaterialTheme.typography.bodyLarge)
                    }
                },
                singleLine = uiConfig.isLarge.not(),
                maxLines = if (uiConfig.isLarge) 10 else 1,
                minLines = if (uiConfig.isLarge) 3 else 1,
                colors = avOutlinedTextFieldColors,
            )
        }
    }

    override fun validate(): Boolean {
        if (uiConfig.baseData.required.not() && state.isBlank()) return true

        uiConfig.baseData.validations.forEach { validation ->
            val isValid = checkRegexRule(state, validation.regex)
            if (isValid.not()) {
                error = UiText.Text(validation.errorMessage)
                return false
            }
        }
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        if (uiConfig.baseData.required.not() && state.isBlank()) return emptyList()

        require(state.isNotBlank()) {
            "OrionUiComponentTextField: ${uiConfig.baseData.title} is required but not inserted, make sure you validate is called !"
        }
        return listOf(OrionKeyStringValue(id = uiConfig.baseData.id, value = state))
    }

    override fun resetValue() {
        state = ""
    }

    private fun checkRegexRule(text: String, regex: String): Boolean {
        val modifiedText = text.replace("\n", " ").replace("\r", "")
        val matcher = Pattern.compile(regex).matcher(modifiedText)
        return matcher.find()
    }
}