pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    includeBuild("build-logic")
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        mavenLocal()
        maven { url = uri("${rootProject.projectDir}/repository/local/") }
        jcenter()
        maven { url = uri("https://jitpack.io") }
        maven { url = uri("https://appboy.github.io/appboy-android-sdk/sdk") }
        maven { url = uri("https://maven.google.com") }
        maven { url = uri("https://oss.sonatype.org/content/repositories/snapshots/") }
    }
}
rootProject.name = "android--app"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")
include(":app")

include(":benchmark")
include(":core:designsystem")
include(":core:domain")
include(":core:data")
include(":core:datastore")
include(":core:common")
include(":core:ui")
include(":core:analytics")
include(":core:adstickybanner")
include(":core:orion")
include(":feature:authentication")
include(":feature:info-center")
include(":feature:info")
include(":feature:shop")
include(":feature:account")
include(":feature:ad")
include(":orion-lib")
include(":chart-lib")
