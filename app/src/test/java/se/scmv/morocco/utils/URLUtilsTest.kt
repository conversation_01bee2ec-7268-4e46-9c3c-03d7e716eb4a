package se.scmv.morocco.utils

import junit.framework.TestCase.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

@RunWith(RobolectricTestRunner::class)
class URLUtilsTest {

    @Test
    fun `ModifyUrlParams single override`() {
        val originalUrl = "https://fakedomain/fr/fakeId?param1=value1&param2=value2"
        val newParams = mapOf("param1" to "newValue1")
        val expectedUrl = "https://fakedomain/fr/fakeId?param1=newValue1&param2=value2"

        val modifiedUrl = URLUtils.modifyUrlParams(originalUrl, newParams)

        assertEquals(expectedUrl, modifiedUrl)
    }

    @Test
    fun `ModifyUrlParams multiple overrides`() {
        val originalUrl = "https://fakedomain/fr/fakeId?param1=value1&param2=value2"
        val newParams = mapOf("param1" to "newValue1", "param2" to "newValue2")
        val expectedUrl = "https://fakedomain/fr/fakeId?param1=newValue1&param2=newValue2"

        val modifiedUrl = URLUtils.modifyUrlParams(originalUrl, newParams)

        assertEquals(expectedUrl, modifiedUrl)
    }

    @Test
    fun `ModifyUrlParams add new param`() {
        val originalUrl = "https://fakedomain/fr/fakeId?param1=value1"
        val newParams = mapOf("param2" to "value2")
        val expectedUrl = "https://fakedomain/fr/fakeId?param1=value1&param2=value2"

        val modifiedUrl = URLUtils.modifyUrlParams(originalUrl, newParams)

        assertEquals(expectedUrl, modifiedUrl)
    }

    @Test
    fun `ModifyUrlParams no override`() {
        val originalUrl = "https://fakedomain/fr/fakeId?param1=value1&param2=value2"
        val newParams = emptyMap<String, String>()
        val expectedUrl = "https://fakedomain/fr/fakeId?param1=value1&param2=value2"

        val modifiedUrl = URLUtils.modifyUrlParams(originalUrl, newParams)

        assertEquals(expectedUrl, modifiedUrl)
    }

    @Test
    fun `ModifyUrlParams override with empty value`() {
        val originalUrl = "https://fakedomain/fr/fakeId?param1=value1&param2=value2"
        val newParams = mapOf("param1" to "")
        val expectedUrl = "https://fakedomain/fr/fakeId?param1=&param2=value2"

        val modifiedUrl = URLUtils.modifyUrlParams(originalUrl, newParams)

        assertEquals(expectedUrl, modifiedUrl)
    }
}