package se.scmv.morocco.utils

import android.net.Uri

object URLUtils {

        fun modifyUrlParams(externalLink: String, newParams: Map<String, String>): String {
                val uri = Uri.parse(externalLink)
                val builder = uri.buildUpon().clearQuery()

                // Create a map of existing query parameters
                val existingParams =
                        uri.queryParameterNames.associateWith { uri.getQueryParameter(it)!! }

                // Merge the existing parameters with the new parameters
                val mergedParams = existingParams.toMutableMap().apply {
                        putAll(newParams)
                }

                // Add merged parameters to the builder
                mergedParams.forEach { (key, value) ->
                        builder.appendQueryParameter(key, value)
                }

                return builder.build().toString()
        }
}