package se.scmv.morocco.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Environment
import java.io.File

/**
 * Created by b<PERSON><PERSON> on 2/5/18.
 */
object ExternalStorageUtils {
    /**
     * Notify the external storage about the added file to the media
     *
     * @param file file added or updated
     */
    fun notifyTheExternalStorage(file: File?, context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            val scanIntent = Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
            val contentUri = Uri.fromFile(file)
            scanIntent.data = contentUri
            context.sendBroadcast(scanIntent)
        } else {
            val intent = Intent(
                Intent.ACTION_MEDIA_MOUNTED,
                Uri.parse("file://" + Environment.getExternalStorageDirectory())
            )
            context.sendBroadcast(intent)
        }
    }
}