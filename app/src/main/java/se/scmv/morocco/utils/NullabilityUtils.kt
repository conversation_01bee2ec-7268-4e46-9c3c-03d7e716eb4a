package se.scmv.morocco.utils

import com.apollographql.apollo3.api.Optional

/**
 * Returns true if the string is null or 0-length.
 *
 * @return true if str is null or zero length
 */
fun isEmpty(charSequence: CharSequence?): Boolean = charSequence.isNullOrEmpty()

fun CharSequence?.isNotEmpty() = !this.isNullOrEmpty()

fun Map<*, *>?.isEmpty(): Boolean = this.isNullOrEmpty()
fun Map<*, *>?.isNotEmpty(): Boolean = !this.isEmpty()

/**
 * Returns `true` if the provided reference is `null` otherwise
 * returns `false`.
 *
 * @return `true` if the provided reference is `null` otherwise
 * `false`
 */
fun Any?.isNull(): Boolean = this === null

/**
 * Returns `true` if the provided reference is non-`null`
 * otherwise returns `false`.
 *
 * @return `true` if the provided reference is non-`null`
 * otherwise `false`
 */
fun Any?.isNotNull(): Boolean = !this.isNull()

fun <T> Collection<T>?.isNotEmpty() = !this.isNullOrEmpty()


inline fun <A, B, R> letNotNull(a: A?, b: B?, transform: (A, B) -> R): R? =
        when (null) {
                a, b -> null
                else -> transform(a, b)
        }

inline fun <A, B, C, R> letNotNull(a: A?, b: B?, c: C?, transform: (A, B, C) -> R): R? =
        when (null) {
                a, b, c -> null
                else -> transform(a, b, c)
        }

fun <T: Any> T?.toOptional(): Optional<T> = this?.let { Optional.Present(it) } ?: Optional.absent()



