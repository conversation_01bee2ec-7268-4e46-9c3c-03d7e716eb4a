package se.scmv.morocco.utils

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 5/16/16.
 */
object Keys {
    //InAPP Update
    const val NEW_IN_APP_UPDATE_MINIMUM_ALLOWED_APP_VERSION: String =
        "new_in_app_update_minimum_allowed_app_version"
    const val IN_APP_UPDATE_SPECIFIC_APP_VERSIONS_TO_UPDATE: String =
        "in_app_update_specific_app_versions_to_update"
    const val IN_APP_UPDATE_MINIMUM_ALLOWED_ANDROID_VERSION: String =
        "in_app_update_minimum_allowed_android_version"
    const val IN_APP_UPDATE_SPECIFIC_ANDROID_VERSIONS_TO_UPDATE: String =
        "in_app_update_specific_android_versions_to_update"
    const val IN_APP_UPDATE_REQUEST_APP_UPDATE_ONCE_EVERY: String =
        "in_app_update_request_app_update_once_every"
    const val IN_APP_UPDATE_FLEXIBLE_UPDATE_TOGGLE: String = "in_app_update_flexible_update_toggle"
    const val IN_APP_UPDATE_IMMEDIATE_UPDATE_TOGGLE: String =
        "in_app_update_immediate_update_toggle"
    const val IN_APP_UPDATE_WEEK_OF_YEAR: String = "in_app_update_weekOfYear"
    const val IN_APP_UPDATE_DAY_OF_YEAR: String = "in_app_update_dayOfYear"
    const val IN_APP_UPDATE_HOUR_OF_DAY: String = "in_app_update_hourOfDay"
}
