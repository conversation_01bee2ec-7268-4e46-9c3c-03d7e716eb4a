package se.scmv.morocco.utils

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.widget.Toast
import se.scmv.morocco.R


/**
 * Created by h<PERSON><PERSON><PERSON><PERSON><PERSON> on 1/16/17.
 */
/**
 * This class was created to handle the different possible actions a user may do on an ad
 * These actions are:
 * - Sharing an ad
 * - Calling the ad publisher
 * - Sending an sms to the ad publisher˙
 * - Sending an email to the ad publisher
 */
object ActionsUtils {


    /**
     * Method that handles analytical tracking related matters for
     * Firebase, appboy for the action send a message
     *
     * @param context : Context of the activity calling this method
     * @param ad
     * @param source: The page where the CTA was clicked
     */


    /**
     * Method that handles the action of sending a Whatsapp message to an ad user
     **
     * @param activity:    Activity calling this method
     * @param ad:         Ad on which the Whatsapp action is being done
     * @param source: The page where the CTA was clicked
     */


    @JvmStatic
    fun whatsappPhoneNumber(
        context: Context,
        phone: String

    ) {
        openWhatsApp(context, phone)
    }


    private fun openWhatsApp(context: Context, phone: String) {
        if (isWhatsappInstalled(context)) {

            val i = Intent(
                Intent.ACTION_VIEW, Uri.parse(
                    "https://wa.me/$phone"
                )
            ).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(i)
        } else {
            Toast.makeText(
                context,
                context.getString(R.string.customer_services_whatsapp_not_installed_title),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun isWhatsappInstalled(context: Context): Boolean {
        val pm = context.packageManager
        return isPackageInstalled("com.whatsapp", pm) || isPackageInstalled("com.whatsapp.w4b", pm)
    }

    private fun isPackageInstalled(packageName: String, packageManager: PackageManager): Boolean {
        try {
            packageManager.getPackageInfo(packageName, 0)
            return true
        } catch (e: PackageManager.NameNotFoundException) {
            return false
        }
    }
}