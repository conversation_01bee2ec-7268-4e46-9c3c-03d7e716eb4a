package se.scmv.morocco.utils

import android.graphics.Bitmap
import android.graphics.Canvas
import android.view.View

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 9/9/16.
 */
object ImageUtils {
        private const val SLASH = "/"

        /**
         * Returns Layout content as screenshot (Image).
         *
         * @param view
         * @return
         */
        fun getViewContentAsBitmap(view: View?): Bitmap? {
                var headerBitmap: Bitmap? = null
                try {
                        if (view != null) {
                                val width = view.width
                                val height = view.height
                                headerBitmap =
                                        Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)
                                view.draw(Canvas(headerBitmap))
                        }
                } catch (e: Exception) {
                        e.printStackTrace()
                }
                return headerBitmap
        }
}