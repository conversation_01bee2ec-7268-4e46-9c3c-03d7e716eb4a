package se.scmv.morocco.utils

import android.content.Context
import android.content.Intent

object Utils {
    const val APP_ENV: String = "app_environment"
    const val APP_FLAVOR: String = "appFlavor"
    const val PREF_DELIVERY_ADDRESS: String = "delivery_address"

    const val PREF_IS_FIRST_LAUNCHED_AFTER_INSTALL: String = "is_first_installed"

    //App Instance Id
    const val FIREBASE_ANALYTICS_INSTANCE_ID: String = "FIREBASE_ANALYTICS_INSTANCE_ID"

    private const val APP_PREFS = "avito_prefs"


    @JvmStatic
    fun savePreference(ctx: Context?, key: String?, value: Any?) {
        if (ctx == null) return

        val prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE)
        if (value is Boolean) prefs.edit().putBoolean(key, value).commit()
        else if (value is Int) prefs.edit().putInt(key, value).commit()
        else if (value is Long) prefs.edit().putLong(key, value).commit()
        else if (value is Float) prefs.edit().putFloat(key, value).commit()
        else if (value is String) prefs.edit().putString(key, value).commit()
    }

    @JvmStatic
    fun removePreference(ctx: Context?, key: String?) {
        if (ctx == null) return

        val prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE)
        prefs.edit().remove(key).commit()
    }

    fun rebirthApp(ctx: Context) {
        val packageManager = ctx.getPackageManager()
        val intent = packageManager.getLaunchIntentForPackage(ctx.getPackageName())
        val componentName = intent!!.getComponent()
        val mainIntent = Intent.makeRestartActivityTask(componentName)
        ctx.startActivity(mainIntent)
        Runtime.getRuntime().exit(0)
    }

    @JvmStatic
    fun getStringPreference(ctx: Context?, key: String?): String? {
        if (ctx == null) return null

        val prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE)
        return prefs.getString(key, null)
    }

    fun getStringPreferenceWithDefaultValue(
        ctx: Context?,
        key: String?,
        defaultValue: String?
    ): String? {
        if (ctx == null) return null

        val prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE)
        return prefs.getString(key, defaultValue)
    }

    //TODO All Preferences helpers should be refactored(Avoid getting Context of Activity,Fragment.....) and get The Application Context
    fun getIntPreference(ctx: Context?, key: String?): Int {
        if (ctx == null) return -1

        val prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE)
        return prefs.getInt(key, -1)
    }

    fun getBooleanPreference(ctx: Context?, key: String?): Boolean {
        if (ctx == null) return false

        val prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE)
        return prefs.getBoolean(key, false)
    }

    fun getBooleanPreference(ctx: Context?, key: String?, defaultValue: Boolean): Boolean {
        if (ctx == null) return defaultValue

        val prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE)
        return prefs.getBoolean(key, defaultValue)
    }
}