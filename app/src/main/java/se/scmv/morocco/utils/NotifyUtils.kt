package se.scmv.morocco.utils

import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.google.android.material.snackbar.Snackbar
import se.scmv.morocco.R


object NotifyUtils {
        /**
         * This method displays a message in a snackbar with a given background color
         *
         * @param view    view to find a parent from to display the snackbar
         * @param message Id of the message to display
         * @param color   Color of the snackbar's bachground, if default black it's equal to -1
         */
        @JvmStatic
        fun displaySnackbar(view: View?, message: String?, color: Int) {
                try {
                        if (view != null) {
                                val snackbar = message?.let {
                                        Snackbar.make(
                                                view,
                                                it,
                                                Snackbar.LENGTH_LONG
                                        )
                                }
                                val snackTextView: TextView =
                                        snackbar?.view?.findViewById(com.google.android.material.R.id.snackbar_text) as TextView
                                snackTextView.maxLines = 5
                                if (color != -1) snackbar.view.setBackgroundColor(
                                        ContextCompat.getColor(
                                                view.context,
                                                color
                                        )
                                )
                                snackbar.show()
                        }
                } catch (ex: Exception) {
                        ex.printStackTrace()
                }
        }

        fun displaySnackbar(view: View?, message: Int, color: Int) {
                if (view != null) {
                        displaySnackbar(view, view.resources.getString(message), color)
                }
        }

        @JvmStatic
        fun displaySuccessSnackbar(view: View?, message: Int) {
                displaySnackbar(view, message, R.color.completion_background)
        }

        @JvmStatic
        fun displaySuccessSnackbar(view: View?, message: String?) {
                displaySnackbar(view, message, R.color.completion_background)
        }

        @JvmStatic
        fun displayInfoSnackbar(view: View?, message: Int) {
                displaySnackbar(view, message, R.color.info_background)
        }

        @JvmStatic
        fun displayErrorSnackbar(view: View?, message: Int) {
                displaySnackbar(view, message, R.color.error_background)
        }

        @JvmStatic
        fun displayErrorSnackbar(view: View?, message: String) {
                displaySnackbar(view, message, R.color.error_background)
        }

        @JvmStatic
        fun displayErrorSnackbarFromString(view: View?, message: String?) {
                displaySnackbar(view, message, R.color.error_background)
        }

        @JvmStatic
        fun displayErrorNoInternet(view: View?) {
                displaySnackbar(view, R.string.offline_message, -1)
        }

        @JvmStatic
        fun displayDefaultSnackbar(view: View?, message: Int) {
                displaySnackbar(view, message, -1)
        }

        @JvmStatic
        fun displayDefaultSnackbar(view: View?, message: String) {
                displaySnackbar(view, message, -1)
        }
}