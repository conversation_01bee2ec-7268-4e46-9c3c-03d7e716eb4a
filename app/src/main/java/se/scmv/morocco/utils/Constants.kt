package se.scmv.morocco.utils

object Constants {
        const val DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"
        const val DATE_FORMAT_ISO_8601 = "yyyy-MM-dd'T'HH:mm:ss'Z'"
        const val DISPLAY_DATE_FORMAT = "d MMM HH:mm"
        const val TODAY_FORMAT = "HH:mm"
        const val AD_DETAILS_REQUEST = 1111
        const val AD_EDIT_REQUEST = 1112
        const val REQUEST_SIGN_IN_FROM_AD_DETAIL = 1113
        const val RESULT_SIGN_IN_FROM_AD_DETAIL = 88888
        const val AD_DETAILS_RESULT_CHECKED_STATE = "AD_DETAILS_RESULT_CHECKED_STATE"
        const val AD_DETAILS_RESULT_AD_ID = "AD_DETAILS_RESULT_AD_ID"
        const val AD_STATUS_ACTIVE = "ACTIVE"
        const val AD_STATUS_PENDING_PAYMENT = "PENDING_PAYMENT"
        const val AD_STATUS_PENDING_REVIEW = "PENDING_REVIEW"
        const val AD_STATUS_INACTIVE = "INACTIVE"
        const val AD_STATUS_REFUSED = "REFUSED"
        const val AD_STATUS_DEACTIVATED = "DEACTIVATED"
        const val AD_STATUS_DELETED = "DELETED"
        const val HALF_OPAQUE = 0.5f
        const val FULLY_OPAQUE = 1f
        const val VAS_USER_PHONE = "vas_user_phone"
        const val VAS_USER_EMAIL = "vas_user_email"
        const val VAS_AD_LIST_ID = "vas_ad_list_id"
        const val VAS_AD_ID = "vas_ad_id"
        const val SELLER_ID = "seller_id"
        const val VAS_AD_TITLE = "vas_ad_title"
        const val VAS_AD_CITY = "vas_ad_city"
        const val VAS_AD_DATE = "vas_ad_date"
        const val VAS_AD_TYPE = "vas_ad_type"
        const val VAS_AD_PRICE = "vas_ad_price"
        const val VAS_AD_CITY_ID = "vas_ad_city_id"
        const val VAS_AD_CATEGORY_ID = "vas_ad_category_id"
        const val VAS_AD_CATEGORY_PARENT = "vas_ad_category_parent"
        const val VAS_AD_IMAGE_PATH = "vas_ad_image_path"
        const val VAS_AD_HAS_PICTURE = "vas_ad_has_picture"
        const val VAS_PACK_ID = "vas_pack_id"
        const val VAS_PAYMENT_KEY = "vas_payment_key"
        const val VAS_PAYMENT_TYPE = "vas_payment_type"
        const val CREDIT_CARD_STRING = "cc"
        const val CASH_STRING = "cash"
        const val SMS_STRING = "sms"

        // VAS UNIT
        const val VAS_UNIT_NAME = "vas_unit_name"
        const val VAS_UNIT_CATEGORY = "vas_unit_category"
        const val VAS_UNIT_ID = "vas_unit_id"
        const val VAS_UNIT_IMAGE = "vas_unit_image"
        const val VAS_UNIT_DURATION = "vas_unit_duration"
        const val VAS_UNIT_TITLE = "vas_unit_title"
        const val VAS_UNIT_DESCRIPTION = "vas_unit_description"
        const val VAS_UNIT_NORMAL_PRICE = "vas_unit_price"
        const val VAS_UNIT_DIFF_PRICE = "vas_unit_diff_price"
        const val VAS_UNITS = "vas_units"
        const val VAS_PAYMENT_LIST = "vas_payment_list"

        //VAS Payment Key
        const val VAS_PAYMENT_METHOD = "pay_meth"

        // VAS SMS PAYMENT
        const val SMS_CODE = "sms_code"
        const val SMS_RECEIVER = "sms_receiver"

        // VAS CREDIT CARD PAYMENT
        const val CREDIT_CARD_REDIRECT_URL = "redirect_url"
        const val CREDIT_CARD_FIRST_NAME = "first_name"
        const val CREDIT_CARD_LAST_NAME = "last_name"
        const val CREDIT_CARD_EMAIL = "email"
        const val CREDIT_CARD_PHONE = "phone"
        const val CREDIT_CARD_SOURCE = "source"

        // VAS CASH PAYMENT
        const val CREDIT_CARD_AMOUNT = "amount"

        //Condition
        const val VAS_IS_FORM_SAVED = "iscardholdersaved"

        //Form input
        const val VAS_CARDHOLDER_FORM_NAME = "cardholder_name"
        const val VAS_CARDHOLDER_FORM_LASTNAME = "cardholder_last_name"
        const val VAS_CARDHOLDER_FORM_EMAIL = "cardholder_email"
        const val VAS_CARDHOLDER_FORM_PHONE = "cardholder_phone"

        // VAS LISTING CONSTANTS
        const val UNRENEWED_AD = 0
        const val BUMPED_AD = 1
        const val HIGHLIGHTED_AD = 2
        const val BUMPED_HIGHLIGHTED_AD = 3
        const val VAS_IMAGE_PRE_PATH = "img/vas/"
        const val LAPZONE_INTENT = "LAPZONE"
        const val IS_AD_EDIT = "IS_AD_EDIT"

        //
        var IS_VAS_AI = "is_vas_ai"

        const val AI_DEFAULT_MAX_PICTURES_COUNT = 8

        // Call Seller Source
        const val BOTTOM_BAR_COMPONENT = "bottom bar"
        const val INFO_CARD_COMPONENT = "info card"
        const val PICTURE_GALLERY_COMPONENT = "picture gallery"

        const val SHOPS_FLAVOR = "shops"
        const val PRIVATE_FLAVOR = "private"

        //touching point
        const val TP_DEFAULT_MAX_STICKY = 2

        //video tagging plan
        const val VIDEO = "video"
        const val STATE = "state"
        const val PLAY_VIDEO = "play"
        const val PAUSE_VIDEO = "pause"
        const val COMPLETE_VIDEO = "complete"
        const val ADVIEW_PAGE = "adview"

        //TOKEN ALLOWED ACCESS
        const val AD_MAX_IMAGES = "adMaxImages"
        const val AD_MAX_VIDEOS = "adMaxVideos"
        const val AD_URGENT_ALLOWED = "adUrgentAllowed"
        const val AD_HOT_DEAL_ALLOWED = "adHotdealAllowed"
        const val DELIVERY_ALLOWED = "deliveryAllowed"
        const val STATS_PER_AD_ALLOWED = "statsPerAdAllowed"
        const val ADS_BULK_DELETE_ALLOWED = "adsBulkDeleteAllowed"
        const val ADS_BOOSTED_FILTER_ALLOWED = "adsBoostedFilterAllowed"
        const val SUPPORT_VIA_WHATSAPP_ALLOWED = "supportViaWhatsappAllowed"
        const val AVITO_TOKEN_ALLOWED = "avitoTokenAllowed"

        //SAV PHONE
        const val SAV_PHONE = "0663531981"

        const val CATEGORY_ICON_URL_PREFIX = "category_"
}