package se.scmv.morocco.utils

import java.text.SimpleDateFormat
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 6/14/17.
 */
object DateUtils {
    const val DATE_FORMAT_YEAR_MONTH_DAY = "yyyy-MM-dd"
    @JvmStatic
    fun getCurrentDateWithFormat(format: String?): String {
        val date = Date()
        return SimpleDateFormat(format).format(date)
    }

    @JvmStatic
    fun formatDateLocalization(
        date: String,
        dateFormat: String = Constants.DISPLAY_DATE_FORMAT
    ): String {
        return try {
            val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern(
                dateFormat,
                Locale.getDefault()
            )
            val zDate = Instant.parse(date)
            val dateTime = zDate.atZone(ZoneId.systemDefault())
            val formattedDate = dateTime.format(formatter)
            formattedDate.toString()

        } catch (e: Exception) {
            ""
        }
    }
}