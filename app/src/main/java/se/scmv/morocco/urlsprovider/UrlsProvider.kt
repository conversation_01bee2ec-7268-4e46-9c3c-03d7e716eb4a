package se.scmv.morocco.urlsprovider

import se.scmv.morocco.BuildConfig

class UrlsProvider {
    companion object {
        private var AppEnv: AppEnvironment = getAppEnvironment()

        fun getAppEnvironment(): AppEnvironment {
            return AppEnvironment.Pro
        }


        fun getApiBaseUrl(): String {
            return if (BuildConfig.DEBUG)
                AppEnv.apiBaseUrl
            else
                AppEnvironment.Pro.apiBaseUrl
        }

        fun getLeadsForceUrl(): String {
            return "https://api.leadsforce.ma/"
        }

        fun getNewAuth(): String {
            return if (BuildConfig.DEBUG)
                AppEnv.newAuthUrl
            else
                AppEnvironment.Pro.newAuthUrl
        }

        fun getConfigApiBaseUrl(): String {
            return AppEnv.configApiBaseUrl
        }

        fun getAdServerUrl(): String {
            return if (BuildConfig.DEBUG)
                AppEnv.adServerUrl
            else
                AppEnvironment.Pro.adServerUrl
        }

        fun getWebBaseUrl(): String {
            return if (BuildConfig.DEBUG)
                AppEnv.webBaseUrl
            else
                AppEnvironment.Pro.webBaseUrl
        }

        fun getThumbBaseUrl(): String {
            return AppEnv.thumbBaseUrl
        }

        fun getFullImageBaseUrl(): String {
            return AppEnv.fullImageBaseUrl
        }

        fun getMAUHostUrl(): String {
            return if (BuildConfig.DEBUG)
                AppEnv.mauHostUrl
            else
                AppEnvironment.Pro.mauHostUrl
        }

        fun getDeliveryBaseUrl(): String {
            return AppEnv.deliveryBaseUrl
        }

        fun getPaymentAPIBaseUrl(): String {
            return AppEnv.paymentAPIBaseUrl
        }

        fun getVasAPIBaseUrl(): String {
            return AppEnv.vasAPIBaseUrl
        }

        fun getVasGalleryBaseUrl(): String {
            return if (BuildConfig.DEBUG)
                AppEnv.vasGalleryBaseUrl
            else
                AppEnvironment.Pro.vasGalleryBaseUrl
        }

        fun getMonetizationImgBaseURL(): String {
            return AppEnv.monetizationImgBaseURL
        }

        fun getLegacyMonetizationImgBaseURL(): String {
            return AppEnv.legacyMonetizationImgBaseURL
        }


        fun getPhoneVerificationBaseUrl(): String {
            return if (BuildConfig.DEBUG)
                AppEnv.phoneVerificationBaseUrl
            else
                AppEnvironment.Pro.phoneVerificationBaseUrl
        }

        fun getMediaConfigBaseUrl(): String {
            return AppEnv.mediaConfigBaseUrl
        }

        fun getMediaAndroidConfigBaseUrl(): String {
            return AppEnv.mediaAndroidConfigBaseUrl
        }

        fun getPayZoneBaseUrl(): String {
            return AppEnv.payZoneBaseUrl
        }

        fun getSearchBaseUrl(): String {

            return if (BuildConfig.DEBUG)
                AppEnv.searchBaseUrl
            else
                AppEnvironment.Pro.searchBaseUrl

        }

        fun getAuthHostBaseUrl(): String {
            return if (BuildConfig.DEBUG)
                AppEnv.authHostBaseUrl
            else
                AppEnvironment.Pro.authHostBaseUrl
        }

        fun getMCAdsHostBaseUrl(): String {
            return AppEnv.mcAdsHostBaseUrl
        }

        fun getMCHostBaseUrl(): String {
            return AppEnv.mcHostBaseUrl
        }

        fun getAdViewAdParamsIconsUrl(name: String): String {
            return AppEnv.adViewAdParamsIconsUrl + name + ".svg"
        }

        fun getGraphQlUrl(): String {
            return AppEnv.graphQlEndpointUrl
        }

        fun getGraphQlWebSocketUrl(): String {
            return AppEnv.graphQlWebSocketUrl
        }

        fun getNewAdInsertUrl(): String {
            return AppEnv.newAdInsertUrl
        }

        fun getNewListingFilterUrl(): String {
            return AppEnv.newListingFilterUrl
        }


    }

}