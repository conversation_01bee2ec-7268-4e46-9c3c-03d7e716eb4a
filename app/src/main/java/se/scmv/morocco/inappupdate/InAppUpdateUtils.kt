package se.scmv.morocco.inappupdate

import android.app.Activity
import android.os.Build
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import com.google.android.material.snackbar.Snackbar
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.InstallStatus
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import se.scmv.morocco.BuildConfig
import se.scmv.morocco.R
import se.scmv.morocco.utils.Keys
import se.scmv.morocco.utils.Utils
import java.util.Calendar


class InAppUpdateUtils {
        companion object {
                internal const val UPDATE_REQUEST_CODE = 530

                fun startImmediateUpdate(activity: Activity, appUpdateManager: AppUpdateManager) {
                        val appUpdateInfoTask = appUpdateManager.appUpdateInfo
                        appUpdateInfoTask.addOnSuccessListener {
                                if (it.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE && it.isUpdateTypeAllowed(
                                                AppUpdateType.IMMEDIATE
                                        )
                                ) {
                                        appUpdateManager.startUpdateFlowForResult(
                                                it,
                                                AppUpdateType.IMMEDIATE,
                                                activity,
                                                UPDATE_REQUEST_CODE
                                        )
                                }
                        }.addOnFailureListener {
                                Log.e("startImmediateUpdate", "Failed to check for update: $it")
                        }
                }

                private fun resumeImmediateUpdate(
                        activity: Activity,
                        appUpdateManager: AppUpdateManager
                ) {
                        val appUpdateInfoTask = appUpdateManager.appUpdateInfo
                        appUpdateInfoTask.addOnSuccessListener {
                                if (it.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                                        appUpdateManager.startUpdateFlowForResult(
                                                it,
                                                AppUpdateType.IMMEDIATE,
                                                activity,
                                                UPDATE_REQUEST_CODE
                                        )
                                }
                        }.addOnFailureListener {
                                Log.e(
                                        "startImmediateUpdate",
                                        "Failed to resume update in progress: $it"
                                )
                        }
                }

                private fun startFlexibleUpdate(
                        activity: Activity,
                        appUpdateManager: AppUpdateManager
                ) {

                        appUpdateManager.registerListener {
                                if (it.installStatus() == InstallStatus.DOWNLOADED) {
                                        displaySnackBarWithBottomMargin(activity, appUpdateManager)
                                }
                        }

                        val appUpdateInfoTask = appUpdateManager.appUpdateInfo
                        appUpdateInfoTask.addOnSuccessListener {
                                if (it.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE && it.isUpdateTypeAllowed(
                                                AppUpdateType.FLEXIBLE
                                        )
                                ) {
                                        appUpdateManager.startUpdateFlowForResult(
                                                it,
                                                AppUpdateType.FLEXIBLE,
                                                activity,
                                                UPDATE_REQUEST_CODE
                                        )
                                }
                        }.addOnFailureListener {
                                Log.e("resumeFlexibleUpdate", "Failed to resume update: $it")
                        }
                }

                private fun resumeFlexibleUpdate(
                        activity: Activity,
                        appUpdateManager: AppUpdateManager
                ) {

                        val appUpdateInfoTask = appUpdateManager.appUpdateInfo
                        appUpdateInfoTask.addOnSuccessListener {
                                if (it.installStatus() == InstallStatus.DOWNLOADED) {
                                        displaySnackBarWithBottomMargin(activity, appUpdateManager)
                                }
                        }.addOnFailureListener {
                                Log.e("startFlexibleUpdate", "Failed to check for update: $it")
                        }
                }

                fun resumeAppUpdate(activity: Activity, appUpdateManager: AppUpdateManager) {
                        if (Utils.getBooleanPreference(
                                        activity,
                                        Keys.IN_APP_UPDATE_FLEXIBLE_UPDATE_TOGGLE
                                )
                        ) {
                                resumeFlexibleUpdate(activity, appUpdateManager)
                        } else if (Utils.getBooleanPreference(
                                        activity,
                                        Keys.IN_APP_UPDATE_IMMEDIATE_UPDATE_TOGGLE
                                )
                        ) {
                                resumeImmediateUpdate(activity, appUpdateManager)
                        }
                }

                private fun startUpdate(activity: Activity, appUpdateManager: AppUpdateManager) {

                        if (Utils.getBooleanPreference(
                                        activity,
                                        Keys.IN_APP_UPDATE_FLEXIBLE_UPDATE_TOGGLE
                                )
                        ) {
                                startFlexibleUpdate(activity, appUpdateManager)
                        } else if (Utils.getBooleanPreference(
                                        activity,
                                        Keys.IN_APP_UPDATE_IMMEDIATE_UPDATE_TOGGLE
                                )
                        ) {
                                startImmediateUpdate(activity, appUpdateManager)
                        }
                }

                fun updateApp(activity: Activity, appUpdateManager: AppUpdateManager) {
                        if (isRequiredToUpdate()) {
                                when (Utils.getStringPreference(
                                        activity,
                                        Keys.IN_APP_UPDATE_REQUEST_APP_UPDATE_ONCE_EVERY
                                )) {
                                        "week" -> {
                                                val cal = Calendar.getInstance()
                                                val currentWeekOfYear =
                                                        cal.get(Calendar.WEEK_OF_YEAR)
                                                val weekOfYear = Utils.getIntPreference(
                                                        activity,
                                                        Keys.IN_APP_UPDATE_WEEK_OF_YEAR
                                                )
                                                if (weekOfYear != currentWeekOfYear) {
                                                        Utils.savePreference(
                                                                activity,
                                                                Keys.IN_APP_UPDATE_WEEK_OF_YEAR,
                                                                currentWeekOfYear
                                                        )
                                                        startUpdate(activity, appUpdateManager)
                                                }
                                        }
                                        "day" -> {
                                                val cal = Calendar.getInstance()
                                                val currentDayOfYear = cal.get(Calendar.DAY_OF_YEAR)
                                                val dayOfYear = Utils.getIntPreference(
                                                        activity,
                                                        Keys.IN_APP_UPDATE_DAY_OF_YEAR
                                                )
                                                if (dayOfYear != currentDayOfYear) {
                                                        Utils.savePreference(
                                                                activity,
                                                                Keys.IN_APP_UPDATE_DAY_OF_YEAR,
                                                                currentDayOfYear
                                                        )
                                                        startUpdate(activity, appUpdateManager)
                                                }
                                        }

                                        "hour" -> {
                                                val cal = Calendar.getInstance()
                                                val currentHourOfYear =
                                                        cal.get(Calendar.HOUR_OF_DAY)
                                                val hourOfYear = Utils.getIntPreference(
                                                        activity,
                                                        Keys.IN_APP_UPDATE_HOUR_OF_DAY
                                                )
                                                if (hourOfYear != currentHourOfYear) {
                                                        Utils.savePreference(
                                                                activity,
                                                                Keys.IN_APP_UPDATE_HOUR_OF_DAY,
                                                                currentHourOfYear
                                                        )
                                                        startUpdate(activity, appUpdateManager)
                                                }
                                        }

                                        else -> startUpdate(activity, appUpdateManager)
                                }
                        }
                }

                private fun isRequiredToUpdate(): Boolean {
                        val appVersionsToUpdate = FirebaseRemoteConfig.getInstance()
                                .getString(Keys.IN_APP_UPDATE_SPECIFIC_APP_VERSIONS_TO_UPDATE)
                        val minimumAllowedAppVersion: String = FirebaseRemoteConfig.getInstance()
                                .getString(Keys.NEW_IN_APP_UPDATE_MINIMUM_ALLOWED_APP_VERSION)

                        val isTargetedAppVersionForUpdate =
                                (appVersionsToUpdate != "" && appVersionsToUpdate.contains("\"${BuildConfig.VERSION_CODE}\"")) || (minimumAllowedAppVersion != "" && minimumAllowedAppVersion.matches(
                                        "-?\\d+(\\.\\d+)?".toRegex()
                                ) && (Integer.parseInt(minimumAllowedAppVersion) > BuildConfig.VERSION_CODE))

                        val androidVersionsToUpdate = FirebaseRemoteConfig.getInstance()
                                .getString(Keys.IN_APP_UPDATE_SPECIFIC_ANDROID_VERSIONS_TO_UPDATE)
                        val minimumAllowedAndroidVersion = FirebaseRemoteConfig.getInstance()
                                .getString(Keys.IN_APP_UPDATE_MINIMUM_ALLOWED_ANDROID_VERSION)

                        val isTargetedAndroidVersionForUpdate =
                                (androidVersionsToUpdate != "" && androidVersionsToUpdate.contains("\"${Build.VERSION.SDK_INT}\"")) || (minimumAllowedAndroidVersion != "" && minimumAllowedAndroidVersion.matches(
                                        "-?\\d+(\\.\\d+)?".toRegex()
                                ) && (Integer.parseInt(minimumAllowedAndroidVersion) > Build.VERSION.SDK_INT))

                        return isTargetedAndroidVersionForUpdate || isTargetedAppVersionForUpdate
                }

                private fun displaySnackBarWithBottomMargin(
                        activity: Activity,
                        appUpdateManager: AppUpdateManager
                ) {
                        val snackbar = Snackbar.make(
                                activity.findViewById(android.R.id.content),
                                activity.getString(R.string.inn_app_update_flexible_update_result_snackbar_text),
                                Snackbar.LENGTH_INDEFINITE
                        ).setAction(activity.getString(R.string.inn_app_update_flexible_update_result_snackbar_action_text)) { appUpdateManager.completeUpdate() }
                        val snackBarView: View = snackbar.view
                        snackBarView.translationY = -convertDpToPixel(90f, activity)
                        snackbar.setActionTextColor(
                                ContextCompat.getColor(
                                        activity,
                                        R.color.colorAccent
                                )
                        )

                        snackbar.show()
                }

                private fun convertDpToPixel(dp: Float, activity: Activity): Float {
                        return dp * (activity.resources.displayMetrics.densityDpi.toFloat() / DisplayMetrics.DENSITY_DEFAULT)
                }

        }
}
