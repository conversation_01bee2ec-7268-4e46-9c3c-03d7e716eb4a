package se.scmv.morocco

import android.util.Log
import androidx.multidex.MultiDexApplication
import androidx.work.Configuration
import androidx.work.WorkManager
import com.braze.Braze
import com.braze.BrazeActivityLifecycleCallbackListener
import com.braze.support.BrazeLogger
import com.facebook.ads.AudienceNetworkAds
import com.facebook.appevents.AppEventsLogger
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.common.GooglePlayServicesNotAvailableException
import com.google.android.gms.common.GooglePlayServicesRepairableException
import com.google.android.gms.security.ProviderInstaller
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.tiktok.TikTokBusinessSdk
import com.tiktok.TikTokBusinessSdk.TTConfig
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.messaging.common.Constants
import se.scmv.morocco.services.TAG
import se.scmv.morocco.utils.AppConfig
import se.scmv.morocco.utils.Utils
import java.security.KeyManagementException
import java.security.NoSuchAlgorithmException
import java.util.concurrent.Executors
import javax.inject.Inject
import javax.net.ssl.SSLContext


/**
 * This is the first executed class
 * Updated by Soufiane on 10/10/16
 * Updated by hb on 12/16/2016.
 */

@HiltAndroidApp
class Avito : MultiDexApplication() {
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    @Inject lateinit var remoteConfig: FirebaseRemoteConfig
    @Inject lateinit var firebaseMessaging: FirebaseMessaging
    @Inject lateinit var sslContext: SSLContext
    @Inject lateinit var braze: Braze


    override fun onCreate() {
        super.onCreate()

        // Initialize TikTok SDK after remote config is loaded
        applicationScope.launch {
            try {
                
                // Use the same cache settings as setupRemoteConfig()
                var cacheExpiration: Long = 21600 // 6 hours in seconds
                if (AppConfig.IS_DEVELOPER_MODE) {
                    cacheExpiration = 0 // Force fetch in developer mode
                }
                
                // Wait for remote config to be fetched and activated with proper cache settings
                remoteConfig.fetch(cacheExpiration).await()
                remoteConfig.activate()
                
                // Get TikTok App IDs from remote config (comma-separated)
                val tikTokAppIds = AppConfig.getTikTokAppIdsFromRemoteConfig()
                
                // Set AppId & TikTok App IDs in application code
                val ttConfig = TTConfig(applicationContext)
                    .setAppId(AppConfig.APPLICATION_ID)
                    .setTTAppId(tikTokAppIds) // TikTok App IDs from remote config (comma-separated)

                // Initialize with optional callback
                TikTokBusinessSdk.initializeSdk(ttConfig, object : TikTokBusinessSdk.TTInitCallback {
                    override fun success() {
                        // Initialization successful
                        Log.d("TikTok", "✅ TikTok SDK initialized successfully with App IDs: $tikTokAppIds")
                    }

                    override fun fail(code: Int, msg: String?) {
                        // Initialization failed
                        Log.e("TikTok", "❌ TikTok SDK initialization failed: code=$code, msg=$msg")
                    }
                })
            } catch (e: Exception) {
                Log.e("TikTok", "Failed to initialize TikTok SDK: ${e.message}")
                // Fallback to BuildConfig value
                val ttConfig = TTConfig(applicationContext)
                    .setAppId(AppConfig.APPLICATION_ID)
                    .setTTAppId(AppConfig.TIKTOK_APP_ID)

                TikTokBusinessSdk.initializeSdk(ttConfig, object : TikTokBusinessSdk.TTInitCallback {
                    override fun success() {
                        Log.d("TikTok", "✅ TikTok SDK initialized successfully with fallback App ID")
                    }

                    override fun fail(code: Int, msg: String?) {
                        Log.e("TikTok", "❌ TikTok SDK initialization failed with fallback: code=$code, msg=$msg")
                    }
                })
            }
        }


        applicationScope.launch(
            CoroutineExceptionHandler { _, error ->
                Log.e(
                    this@Avito::class.TAG,
                    "Critical error during app initialization: ${error.message}")
            }
        ) {
            try {
                // Initialize critical services first
                initCriticalServices()

                // Initialize non-critical services
                initNonCriticalServices()

                // Initialize analytics and tracking
                initAnalytics()

                // Initialize Firebase
                initFirebase()
            } catch (e: Exception) {
                Log.e(
                    this@Avito::class.TAG,
                    "Failed to initialize app: ${e.message}",
                )
                // add crash reporting
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        }

        val workManagerConfig = Configuration.Builder()
            .setMinimumLoggingLevel(Log.VERBOSE)
            .setExecutor(Executors.newSingleThreadExecutor())
            .build()
        WorkManager.initialize(this, workManagerConfig)
    }

    private suspend fun initCriticalServices() {
        try {
            FirebaseApp.initializeApp(this)
            initSSL()
            setupRemoteConfig()
        } catch (e: Exception) {
            Log.e(
                this@Avito::class.TAG,
                "Failed to initialize critical services: ${e.message}",
            )
            throw e // Re-throw as this is critical
        }
    }

    private suspend fun initNonCriticalServices() {
        try {
            BrazeLogger.logLevel = BrazeLogger.SUPPRESS
            MobileAds.initialize(this)
            AudienceNetworkAds.initialize(this)
            registerActivityLifecycleCallbacks(BrazeActivityLifecycleCallbackListener())
        } catch (e: Exception) {
            Log.e(
                this@Avito::class.TAG,
                "Failed to initialize non-critical services: ${e.message}",
            )
        }
    }

    private suspend fun initAnalytics() {
        try {
            AppEventsLogger.activateApp(this)
            AnalyticsManager.initialize(applicationContext)
        } catch (e: Exception) {
            // Don't re-throw as analytics are non-critical
            Log.e(
                this@Avito::class.TAG,
                "Failed to initialize analytics: ${e.message}",
            )
        }
    }

    private fun initSSL() {
        try {
            // Install the provider in the main thread
            try {
                ProviderInstaller.installIfNeeded(applicationContext)
            } catch (e: GooglePlayServicesRepairableException) {
                // Google Play services is not installed, disabled, or the version is too old
                Log.e("Avito", "Google Play Services needs to be updated", e)
                // Continue with SSL setup anyway as other features might work
            } catch (e: GooglePlayServicesNotAvailableException) {
                // Google Play services is not available
                Log.e("Avito", "Google Play Services not available", e)
                // Continue with SSL setup anyway as other features might work
            }

            // Initialize SSL context regardless of provider installation
            sslContext.init(null, null, null)
            sslContext.createSSLEngine()
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        } catch (e: KeyManagementException) {
            e.printStackTrace()
        }
    }

    private suspend fun initFirebase() {
        runCatching {
            val fcmToken = firebaseMessaging.token.await()
            setupNewMessagingFirebaseToken(fcmToken)
            braze.registeredPushToken = fcmToken
        }.onFailure {
            Log.e(
                "FirebaseMessaging:",
                "Fetching FCM registration token failed",
                it
            )
        }
        runCatching {
            val instanceId = FirebaseAnalytics.getInstance(applicationContext).appInstanceId.await()
            Utils.savePreference(
                applicationContext,
                Utils.FIREBASE_ANALYTICS_INSTANCE_ID,
                instanceId
            )
        }.onFailure {
            Log.e(
                "FirebaseAnalytics",
                "Exception while refreshing FirebaseAnalytics appInstanceId",
                it
            )
        }
    }

    // TODO Should be called after authentication and at app startup
    private suspend fun setupNewMessagingFirebaseToken(token: String) {
        val currentToken = Utils.getStringPreference(
            this,
            Constants.FIREBASE_MESSAGING_TOKEN
        )
    }

    private fun setupRemoteConfig() {
        var cacheExpiration: Long = 21600 // 6 hours in seconds.
        // If your app is using developer mode, cacheExpiration is set to 0,
        // so each fetch will retrieve values from the service.
        if (BuildConfig.DEBUG) {
            cacheExpiration = 0
        }
        val task = remoteConfig.fetch(cacheExpiration)
        task.addOnSuccessListener { _: Void? ->
            remoteConfig.activate()
        }
    }

    override fun onLowMemory() {
        super.onLowMemory()
        if (applicationScope.isActive) {
            applicationScope.cancel()
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        if (applicationScope.isActive) {
            applicationScope.cancel()
        }
    }
}