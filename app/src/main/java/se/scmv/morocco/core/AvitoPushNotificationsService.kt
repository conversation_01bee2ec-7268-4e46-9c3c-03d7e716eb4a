package se.scmv.morocco.core

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.BitmapFactory
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.app.TaskStackBuilder
import androidx.core.net.toUri
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import se.scmv.morocco.R
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.ui.AppDeepLinks
import se.scmv.morocco.utils.isNotNull

class AvitoPushNotificationsService : FirebaseMessagingService() {

    override fun onNewToken(token: String) {
        super.onNewToken(token)
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        sendNotification(remoteMessage)
    }

    private fun isAvitoPushNotification(remoteMessage: RemoteMessage): Boolean {
        return !remoteMessage.data["body"].isNullOrEmpty() && !remoteMessage.data["title"].isNullOrEmpty() && (!remoteMessage.data["redirect_to_my_ad_id"].isNullOrEmpty() || !remoteMessage.data["redirect_to_list_id"].isNullOrEmpty() || remoteMessage.data["redirect_to_my_account"].isNotNull() || remoteMessage.data["redirect_to_ad_listing_vas"].isNotNull())
    }

    private fun sendNotification(remoteMessage: RemoteMessage) {
        if (!isAvitoPushNotification(remoteMessage)) {
            return
        }
        val adId = remoteMessage.data["redirect_to_my_ad_id"]
        val redirectToMyAccount = remoteMessage.data["redirect_to_my_account"]
        val listIdAdView = remoteMessage.data["redirect_to_list_id"]
        val vasAdId = remoteMessage.data["redirect_to_ad_listing_vas"]
        val vasPackId = remoteMessage.data["redirect_to_ad_listing_vas_pack_id"]
        val vasApplication = remoteMessage.data["redirect_to_ad_listing_vas_pack_application"]
        val messageType = remoteMessage.data["type"]
        val notificationBody = remoteMessage.data["body"]

        val activityIntent = Intent(applicationContext, MainActivity::class.java)
        val uri = if (!listIdAdView.isNullOrEmpty()) {
            (AppDeepLinks.AD_VIEW + "/$listIdAdView").toUri()
        } else if (!vasAdId.isNullOrEmpty()) {
            val nonVasApplication = if (!vasApplication.isNullOrEmpty()) {
                VasPacksApplication.valueOfOrDefault(
                    value = vasApplication,
                    default = VasPacksApplication.PUSH
                ).name
            } else VasPacksApplication.PUSH.name
            (AppDeepLinks.AD_BUMP + "/NOTIFICATION/$vasAdId/$nonVasApplication?pack_id=$vasPackId").toUri()
        } else if (!redirectToMyAccount.isNullOrEmpty()) {
            (AppDeepLinks.ACCOUNT_ADS + "?status=$messageType").toUri()
        } else null
        activityIntent.data = uri ?: return
        val pendingIntent = TaskStackBuilder.create(applicationContext).run {
            addNextIntentWithParentStack(activityIntent)
            getPendingIntent(
                0,
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
                    PendingIntent.FLAG_UPDATE_CURRENT
                } else PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )
        }
        val largeIcon = BitmapFactory.decodeResource(resources, R.drawable.avito_logo)
        val notification = NotificationCompat.Builder(applicationContext, "Services")
            .setAutoCancel(true)
            .setDefaults(Notification.DEFAULT_LIGHTS or Notification.DEFAULT_VIBRATE)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setWhen(System.currentTimeMillis())
            .setLargeIcon(largeIcon)
            .setSmallIcon(R.drawable.avito_logo)
            .setContentTitle(remoteMessage.data["title"])
            .setStyle(NotificationCompat.BigTextStyle().bigText(notificationBody))
            .setOnlyAlertOnce(false)
            .setContentText(notificationBody)
            .setContentIntent(pendingIntent)
            .build()

        val notificationManagerCompat = NotificationManagerCompat.from(applicationContext)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = "Services"
            val channel = NotificationChannel(
                channelId,
                "Services",
                NotificationManager.IMPORTANCE_HIGH
            )
            notificationManagerCompat.createNotificationChannel(channel)
        }
        if (ActivityCompat.checkSelfPermission(
                applicationContext,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            notificationManagerCompat.notify(
                "Services",
                (System.currentTimeMillis() / 1000).toInt(),
                notification
            )
        }
    }
}