package se.scmv.morocco.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import androidx.navigation.toRoute
import kotlinx.serialization.Serializable
import se.scmv.morocco.account.presentation.navigation.AccountAdsRoute
import se.scmv.morocco.account.presentation.navigation.ChatRoute
import se.scmv.morocco.account.presentation.navigation.accountGraph
import se.scmv.morocco.ad.navigation.AdInsertRoute
import se.scmv.morocco.ad.navigation.AdViewRoute
import se.scmv.morocco.ad.navigation.VasRoute
import se.scmv.morocco.ad.navigation.adNavGraph
import se.scmv.morocco.authentication.presentation.navigation.AuthScreen
import se.scmv.morocco.authentication.presentation.navigation.authNavGraph
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.info.navigation.infoNavGraph
import se.scmv.morocco.orion.presentation.OrionFiltersRoute
import se.scmv.morocco.orion.presentation.OrionFiltersViewModel
import se.scmv.morocco.shoppage.presentation.ShopPageRoute
import se.scmv.morocco.shoppage.presentation.shoppage.ShopScreenRoute

@Serializable
data object Home

@Serializable
data object HomeScreen

@Serializable
data class FiltersScreen(val clickedFilterId: String? = null)

@Serializable
data class WebViewRoute(val title: String?, val url: String)

@Composable
fun AvitoNavHost(
    account: Account,
    navController: NavHostController,
    updateAppLanguage: () -> Unit,
    openContactSupport: () -> Unit,
    onUpdateAppClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = Home,
        modifier = modifier
    ) {
        authNavGraph(
            navController = navController,
            navigateToHome = {
                navController.navigate(Home) {
                    popUpTo(AuthScreen.NAV_GRAPH_ROUTE) {
                        inclusive = true
                    }
                }
            },
            navigateToWebViewScreen = { title, url ->
                navController.navigate(WebViewRoute(title = title, url = url))
            }
        )
        navigation<Home>(startDestination = HomeScreen) {
            composable<HomeScreen> { backStackEntry ->
                val parentEntry = remember(backStackEntry) {
                    navController.getBackStackEntry(Home)
                }
                val filtersViewModel = hiltViewModel<OrionFiltersViewModel>(parentEntry)
                HomeScreen(
                    account = account,
                    navigate = { navController.navigate(it) },
                    navigateToAuth = { navController.navigate(AuthScreen.NAV_GRAPH_ROUTE) },
                    navigateToAdInsert = {
                        if (account.isLogged()) {
                            navController.navigate(AdInsertRoute())
                        } else navController.navigate(AuthScreen.NAV_GRAPH_ROUTE)
                    },
                    notifyCategoryChanged = { categoryId, adTypeKey ->
                        filtersViewModel.onCategoryChanged(categoryId, adTypeKey)
                    },
                    notifySearchSuggestionSelected = filtersViewModel::preselectFilters,
                    notifyBookmarkedSearchSelected = { bookmarkedSearch ->
                        filtersViewModel.onBookmarkedSearchSelected(bookmarkedSearch)
                    },
                    notifyCancelExtendedDeliveryClicked = filtersViewModel::onCancelExtendedDeliveryClicked,
                    notifyCancelExtendedSearchClicked = filtersViewModel::onCancelExtendedSearchClicked,
                    updateAppLanguage = updateAppLanguage,
                    openContactSupport = openContactSupport,
                    onUpdateAppClicked = onUpdateAppClicked,
                )
            }
            composableWithAnimation<FiltersScreen> { backStackEntry ->
                val route = backStackEntry.toRoute<FiltersScreen>()
                val parentEntry = remember(backStackEntry) {
                    navController.getBackStackEntry(Home)
                }
                val viewModel = hiltViewModel<OrionFiltersViewModel>(parentEntry)
                OrionFiltersRoute(
                    account = account,
                    clickedFilterId = route.clickedFilterId,
                    viewModel = viewModel,
                    navigateBack = navController::navigateUp,
                    navigateBackAndToListing = {
                        navController.navigateUp()
                        // TODO appState.navigateToTopLevelDestination(TopLevelDestination.LISTING)
                    },
                    navigateToAuth = {
                        navController.navigate(AuthScreen.NAV_GRAPH_ROUTE)
                    }
                )
            }
        }
        adNavGraph(
            account = account,
            navController = navController,
            navigateToAuthentication = {
                navController.navigate(AuthScreen.NAV_GRAPH_ROUTE)
            },
            navigateToAccountAds = {
                navController.navigate(route = AccountAdsRoute(), navOptions = it)
            },
            navigateToWebViewScreen = { title, url, navOptions ->
                navController.navigate(
                    route = WebViewRoute(title = title, url = url),
                    navOptions = navOptions
                )
            },
            navigateToShopPage = { storeId ->
                navController.navigate(ShopPageRoute(storeId = storeId))
            },
            navigateToChat = { conversationId ->
                navController.navigate(ChatRoute(conversationId))
            }
        )
        accountGraph(
            account = account,
            navController = navController,
            navigateToAuthentication = { navController.navigate(AuthScreen.NAV_GRAPH_ROUTE) },
            navigateToAdInsert = { adId, toImageStep ->
                navController.navigate(AdInsertRoute(adId = adId, goToImageStep = toImageStep))
            },
            navigateToVas = { adId, adCategory, adType, application ->
                navController.navigate(
                    VasRoute(
                        from = VasRoute.From.ACCOUNT,
                        adId = adId,
                        application = application,
                    )
                )
            },
            navigateToAdview = {
                navController.navigate(AdViewRoute(listId = it))
            }
        )
        composableWithAnimation<ShopPageRoute> {
            ShopScreenRoute(
                onBackPressed = {
                    navController.navigateUp()
                },
                navigateToAdView = {
                    navController.navigate(AdViewRoute(listId = it))
                },
                navigateToAuth = {
                    navController.navigate(AuthScreen.NAV_GRAPH_ROUTE)
                }
            )
        }
        infoNavGraph(
            navController = navController,
            navigateToWebView = { title, url ->
                navController.navigate(WebViewRoute(title = title, url = url))
            }
        )
        composableWithAnimation<WebViewRoute> {
            val route = it.toRoute<WebViewRoute>()
            WebViewScreen(
                url = route.url,
                title = route.title,
                navigateBack = { navController.navigateUp() }
            )
        }
    }
}