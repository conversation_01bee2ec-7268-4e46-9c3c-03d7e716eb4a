package se.scmv.morocco.ui

import androidx.annotation.StringRes
import se.scmv.morocco.R
import se.scmv.morocco.account.presentation.navigation.AccountBookmarksRoute
import se.scmv.morocco.account.presentation.navigation.AccountMasterRoute
import se.scmv.morocco.account.presentation.navigation.AccountStatisticsRoute
import se.scmv.morocco.account.presentation.navigation.MessagingRoute
import se.scmv.morocco.ad.navigation.ListingRoute
import kotlin.reflect.KClass

/**
 * Type for the top level destinations in the application. Contains metadata about the destination
 * that is used in the top app bar and common navigation UI.
 *
 * @param selectedIcon The icon to be displayed in the navigation UI when this destination is
 * selected.
 * @param unselectedIcon The icon to be displayed in the navigation UI when this destination is
 * not selected.
 * @param iconTextId Text that to be displayed in the navigation UI.
 * @param route The route to use when navigating to this destination.
 */
sealed class TopLevelDestination(
    val selectedIcon: Int,
    val unselectedIcon: Int,
    @StringRes val iconTextId: Int,
    val route: KClass<*>,
) {

    fun getIcon(selected: Boolean): Int = if (selected) selectedIcon else unselectedIcon

    data object LISTING : TopLevelDestination(
        selectedIcon = R.drawable.ic_menu_home,
        unselectedIcon = R.drawable.ic_menu_home,
        iconTextId = R.string.navigation_home,
        route = ListingRoute::class,
    )

    data object BOOKMARKS : TopLevelDestination(
        selectedIcon = R.drawable.ic_outlined_heart,
        unselectedIcon = R.drawable.ic_outlined_heart,
        iconTextId = R.string.tab_favorites,
        route = AccountBookmarksRoute::class,
    )

    data object STATS : TopLevelDestination(
        selectedIcon = R.drawable.ic_outlined_statistics,
        unselectedIcon = R.drawable.ic_outlined_statistics,
        iconTextId = R.string.menu_my_statistics,
        route = AccountStatisticsRoute::class,
    )

    data object MESSAGING : TopLevelDestination(
        selectedIcon = R.drawable.ic_chat_white,
        unselectedIcon = R.drawable.ic_chat_white,
        iconTextId = R.string.navigation_message,
        route = MessagingRoute::class,
    )

    data object ACCOUNT : TopLevelDestination(
        selectedIcon = R.drawable.ic_outlined_user,
        unselectedIcon = R.drawable.ic_outlined_user,
        iconTextId = R.string.navigation_profile,
        route = AccountMasterRoute::class,
    )

    companion object {
        fun entries(): Array<TopLevelDestination> {
            return arrayOf(LISTING, BOOKMARKS, STATS, MESSAGING, ACCOUNT)
        }
    }
}



