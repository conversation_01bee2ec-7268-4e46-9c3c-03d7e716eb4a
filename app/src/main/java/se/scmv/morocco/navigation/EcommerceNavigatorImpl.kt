package se.scmv.morocco.navigation

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.navigation.EcommerceNavigator
import se.scmv.morocco.utils.BuyEcommerceProductUtils
import javax.inject.Inject

class EcommerceNavigatorImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : EcommerceNavigator {

    override fun buyEcommerceProduct(
        ad: AdDetails.Details,
        adUuid: String
    ) {
        BuyEcommerceProductUtils.buyEcommerceProduct(
            context = context,
            ad = ad,
            adUuid = adUuid
        )
    }
}
