package se.scmv.morocco.navigation

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import se.scmv.morocco.domain.navigation.AppAction
import se.scmv.morocco.utils.ActionsUtils
import javax.inject.Inject

class AppActionImpl @Inject constructor(
    @ApplicationContext private val context: Context
): AppAction {

    override suspend fun openWhatsappPhoneNumber(phoneNumber: String) {
        ActionsUtils.whatsappPhoneNumber(context = context, phoneNumber)
    }
}