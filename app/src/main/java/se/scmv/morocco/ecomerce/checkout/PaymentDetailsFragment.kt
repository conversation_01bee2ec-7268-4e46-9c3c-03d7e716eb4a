package se.scmv.morocco.ecomerce.checkout

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import se.scmv.morocco.databinding.PaymentDetailsFragmentBinding
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.utils.Constants
import se.scmv.morocco.utils.Utils
import javax.inject.Inject

@AndroidEntryPoint
class PaymentDetailsFragment : Fragment() {

    companion object {
        fun newInstance(extras: Bundle): PaymentDetailsFragment {
            return PaymentDetailsFragment().apply {
                arguments = extras
            }
        }
    }

    private var _binding: PaymentDetailsFragmentBinding? = null
    val binding: PaymentDetailsFragmentBinding
        get() = _binding!!

    var adId: String? = null

    @Inject
    lateinit var accountRepository: AccountRepository

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = PaymentDetailsFragmentBinding.inflate(inflater, container, false)
        return _binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        adId = arguments?.getString(Constants.VAS_AD_ID)

        setAccountDetails(view)
    }


    private fun setAccountDetails(view: View) {
        lifecycleScope.launch {
            (accountRepository.currentAccount.firstOrNull() as? Account.Connected)?.let { account ->
                val account = account.connectedContact()
                binding.phoneField.setText(account.phone ?: "")
                binding.accountEmailField.setText(account.email)
                binding.fullNameField.setText(account.name)
                Utils.getStringPreference(
                    view.context,
                    Utils.PREF_DELIVERY_ADDRESS
                )?.let {
                    binding.deliveryAddressField.setText(it)
                }
            }
        }

    }
    fun validateFields(): Boolean {
        return with(binding) {
            if (deliveryAddressField.validate()) {
                Utils.savePreference(
                    context,
                    Utils.PREF_DELIVERY_ADDRESS,
                    deliveryAddressField.getValue().editTextValue
                )
            }
            phoneField.validate() && accountEmailField.validate() && deliveryAddressField.validate() && fullNameField.validate()
        }
    }
}