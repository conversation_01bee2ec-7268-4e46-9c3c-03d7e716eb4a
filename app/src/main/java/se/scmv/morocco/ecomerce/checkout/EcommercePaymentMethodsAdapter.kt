import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import se.scmv.morocco.R
import se.scmv.morocco.type.ECommercePaymentMethod


class EcommercePaymentMethodsAdapter(context: Context, data: List<ECommercePaymentMethod>) :
        RecyclerView.Adapter<EcommercePaymentMethodsAdapter.ViewHolder>() {
        private val mPaymentMethods: List<ECommercePaymentMethod>
        private val mInflater: LayoutInflater
        val context = context
        var mSelectedItem = -1
        var chosenPaymentMethod: ECommercePaymentMethod? = null

        // inflates the row layout from xml when needed
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
                val view: View =
                        mInflater.inflate(R.layout.item_ecommerce_payment_method, parent, false)
                return ViewHolder(view)
        }

        // binds the data to the TextView in each row
        override fun onBindViewHolder(holder: ViewHolder, position: Int) {

                val currentPaymentMethod = mPaymentMethods[position]

                holder.container.setOnClickListener { holder.radioButton.performClick() }
                holder.radioButton.isChecked = position == mSelectedItem
                holder.radioButton.tag = position
                holder.radioButton.setOnClickListener { v ->
                        chosenPaymentMethod = currentPaymentMethod
                        mSelectedItem = v.tag as Int
                        notifyDataSetChanged()
                }
                holder.radioButton.setOnCheckedChangeListener { buttonView, isChecked ->
                        if (isChecked) {

                                selectVisual(holder.radioButton, holder.container)
                                chosenPaymentMethod = currentPaymentMethod
                                mSelectedItem = buttonView.tag as Int
                        } else {
                                unSelectVisual(holder.radioButton, holder.container)

                        }
                }
                when (currentPaymentMethod) {
                        ECommercePaymentMethod.CC -> {

                                Glide.with(context)
                                        .load(
                                                ContextCompat.getDrawable(
                                                        holder.container.context,
                                                        R.drawable.ic_ecommerce_card
                                                )
                                        )
                                        .into(holder.paymentMethodImage)

                                holder.paymentMethodsubTitle.text =
                                        context.getString(R.string.subtitle_CC)
                                holder.paymentMethodTitle.text =
                                        context.getString(R.string.webview_title_credit_card_payment)

                        }
                        ECommercePaymentMethod.COD -> {

                                Glide.with(context)
                                        .load(
                                                ContextCompat.getDrawable(
                                                        context,
                                                        R.drawable.ic_ecommerce_cash
                                                )
                                        )
                                        .into(holder.paymentMethodImage)
                                holder.paymentMethodsubTitle.text =
                                        context.getString(R.string.subtitle_COD)
                                holder.paymentMethodTitle.text =
                                        context.getString(R.string.cash_payment)

                        }
                        else -> {}
                }
        }

        // total number of rows
        override fun getItemCount(): Int {
                return mPaymentMethods.size
        }

        private fun selectVisual(radioButton: RadioButton, container: LinearLayout) {
                val colorStateList = ColorStateList(
                        arrayOf(intArrayOf(android.R.attr.state_enabled)),
                        intArrayOf(ContextCompat.getColor(context, R.color.orionBlue))
                )
                //radioButton.supportButtonTintList = colorStateList
                container.background =
                        ContextCompat.getDrawable(context, R.drawable.payment_method_selected_bg)
        }

        private fun unSelectVisual(radioButton: RadioButton, container: LinearLayout) {
                val colorStateList = ColorStateList(
                        arrayOf(intArrayOf(android.R.attr.state_enabled)),
                        intArrayOf(ContextCompat.getColor(context, R.color.orionGrey))
                )
                //radioButton.supportButtonTintList = colorStateList
                container.background =
                        ContextCompat.getDrawable(context, R.drawable.payment_method_bg)

        }

        // stores and recycles views as they are scrolled off screen
        inner class ViewHolder(itemView: View) :
                RecyclerView.ViewHolder(itemView) {
                var radioButton: RadioButton
                var container: LinearLayout
                var paymentMethodImage: ImageView
                var paymentMethodTitle: TextView
                var paymentMethodsubTitle: TextView

                init {
                        radioButton = itemView.findViewById(R.id.vas_payment_method_chosen)
                        container = itemView.findViewById(R.id.payment_method_container)
                        paymentMethodImage = itemView.findViewById(R.id.vas_payment_method_image)
                        paymentMethodsubTitle =
                                itemView.findViewById(R.id.vas_payment_method_subname)
                        paymentMethodTitle = itemView.findViewById(R.id.vas_payment_method_name)
                }
        }

        // convenience method for getting data at click position
        fun getItem(id: Int): ECommercePaymentMethod {
                return mPaymentMethods[id]
        }


        // data is passed into the constructor
        init {
                mInflater = LayoutInflater.from(context)
                mPaymentMethods = data
        }

        class MarginItemDecoration(private val spaceSize: Int) : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                        outRect: Rect, view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                ) {
                        with(outRect) {
                                if (parent.getChildAdapterPosition(view) == 0) {
                                        top = spaceSize
                                }
                                left = spaceSize
                                right = spaceSize
                                bottom = spaceSize
                        }
                }
        }
}