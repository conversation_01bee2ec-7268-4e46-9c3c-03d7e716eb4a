package se.scmv.morocco.ecomerce.checkout

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.apollographql.apollo3.ApolloClient
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import se.scmv.morocco.FinalizePurchaseOrderMutation
import se.scmv.morocco.GetEcommerceDetailsQuery
import se.scmv.morocco.GetPurchaseOrderSummaryQuery
import se.scmv.morocco.R
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.avitov2.vas.presentation.activities.EmbeddedWebViewActivity
import se.scmv.morocco.databinding.ActivityPaymentDetailsBinding
import se.scmv.morocco.type.ECommercePaymentMethod
import se.scmv.morocco.type.FinalizePurchaseOrderInput
import se.scmv.morocco.type.PurchaseOrderItemInput
import se.scmv.morocco.type.PurchaseOrderSummaryInput
import se.scmv.morocco.type.ShippingInfoInput
import se.scmv.morocco.utils.Constants
import se.scmv.morocco.utils.NotifyUtils
import javax.inject.Inject


@AndroidEntryPoint
class PaymentDetailsActivity : AppCompatActivity() {

    private var _binding: ActivityPaymentDetailsBinding? = null
    private val binding: ActivityPaymentDetailsBinding
        get() = _binding!!

    var adId: String? = null
    var name: String? = null
    var email: String? = null
    var address: String? = null
    var phone: String? = null
    var ad: GetPurchaseOrderSummaryQuery.Ad? = null
    var paymentMethodsList: List<ECommercePaymentMethod>? = null

    @Inject
    lateinit var apolloClient: ApolloClient

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityPaymentDetailsBinding.inflate(layoutInflater)
        setContentView(_binding?.root)
        applyEdgeToEdge(R.id.root)
        initToolBar()
        title = getString(R.string.cart)
        adId = intent.extras?.getString(Constants.VAS_AD_ID)
        intent.extras?.let { displayFormFragment(it) }

        binding.payButton.setOnClickListener {
            proceedToPayment(adId)
        }
        binding.layoutError.retryButton.setOnClickListener {
            initPurchaseOrderSummary()
        }
        initPurchaseOrderSummary()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    fun initToolBar() {
        setSupportActionBar(binding.toolbar.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(true)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }

    private fun displayFormFragment(bundle: Bundle) {
        supportFragmentManager
            .beginTransaction()
            .setCustomAnimations(
                R.anim.enter,
                R.anim.pop_enter,
                R.anim.pop_exit,
                R.anim.exit
            )
            .add(
                R.id.fragment_container_view,
                PaymentDetailsFragment.newInstance(bundle)
            )
            .commit()
    }

    private fun proceedToPayment(adId: String?) {
        val fm: FragmentManager = supportFragmentManager

        val fragInstance: Fragment? =
            fm.findFragmentById(R.id.fragment_container_view)

        val chosenPaymentMethod =
            (fragInstance as PaymentSummaryFragment).getChosenPaymentMethod()
        if (chosenPaymentMethod != null) {
            lifecycleScope.launch {

                val listPayment = ArrayList<PurchaseOrderItemInput>()
                val paymentItem = PurchaseOrderItemInput(adId = adId!!, quantity = 1)
                listPayment.add(paymentItem)

                val recipientInfo = ShippingInfoInput(
                    fullName = name.orEmpty(),
                    telephone = phone.orEmpty(),
                    email = email.orEmpty(),
                    address = address.orEmpty()
                )

                val paymentInput = FinalizePurchaseOrderInput(
                    items = listPayment,
                    shippingInfo = recipientInfo,
                    paymentMethod = chosenPaymentMethod
                )

                try {
                    val paymentQuery = apolloClient.mutation(
                        FinalizePurchaseOrderMutation(paymentInput)
                    ).execute()
                    if (paymentQuery?.hasErrors() == false) {
                        var analyticsPayload = HashMap<String?, String?>()
                        ad?.adId?.let { analyticsPayload.put("ad_id", it) }
                        ad?.price?.let {
                            analyticsPayload.put(
                                "ad_price",
                                it.withoutCurrency.toString()
                            )
                        }
                        intent.extras?.getString(Constants.SELLER_ID)?.let {
                            analyticsPayload.put("seller_id", it)
                        }
                        analyticsPayload["payment_method"] =
                            paymentInput.paymentMethod.rawValue
                        AnalyticsManager
                            .instance?.logEvent(
                                "payment",
                                analyticsPayload,
                                true
                            )
                        if (paymentInput.paymentMethod == ECommercePaymentMethod.CC) {

                            // Create and fill bundle with response information
                            val creditCardExtras = Bundle()

                            creditCardExtras.putString(
                                EmbeddedWebViewActivity.WEBVIEW_CONTENT_TYPE,
                                EmbeddedWebViewActivity.ECOMMERCE
                            )
                            creditCardExtras.putString(
                                EmbeddedWebViewActivity.ECOMMERCE_PAYMENT_URL,
                                paymentQuery.data?.finalizePurchaseOrder?.paymentURL
                            )

                            creditCardExtras.putAll(intent.extras)
                            // Start new activity
                            val creditCardPayment = Intent(
                                this@PaymentDetailsActivity,
                                EmbeddedWebViewActivity::class.java
                            )
                            creditCardPayment.putExtras(creditCardExtras)
                            startActivity(creditCardPayment)
                            finish()

                        } else {
                            val intent = Intent(
                                this@PaymentDetailsActivity,
                                PaymentSuccessPage::class.java
                            )
                            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                            finish()
                            <EMAIL>(intent)
                        }
                    } else {
                        NotifyUtils.displayErrorSnackbar(
                            binding.root,
                            R.string.common_unexpected_error_verify_and_try_later
                        )
                    }
                } catch (e: Exception) {
                    NotifyUtils.displayErrorSnackbar(
                        binding.root,
                        R.string.common_unexpected_error_verify_and_try_later
                    )
                }
            }
        } else {
            NotifyUtils.displayErrorSnackbar(
                binding.root,
                R.string.please_chose_payment_method
            )
        }
    }

    private fun displaySummaryFragment(bundle: Bundle) {
        if ((supportFragmentManager.fragments[0] as PaymentDetailsFragment).validateFields()) {
            name =
                (supportFragmentManager.fragments[0] as PaymentDetailsFragment).binding.fullNameField.getValue().value.toString()
            email =
                (supportFragmentManager.fragments[0] as PaymentDetailsFragment).binding.accountEmailField.getValue().value.toString()
            phone =
                (supportFragmentManager.fragments[0] as PaymentDetailsFragment).binding.phoneField.getValue().value.toString()
            address =
                (supportFragmentManager.fragments[0] as PaymentDetailsFragment).binding.deliveryAddressField.getValue().value.toString()
            binding.payButton.visibility = View.VISIBLE
            binding.continueButton.visibility = View.GONE
            supportFragmentManager
                .beginTransaction()
                .setCustomAnimations(
                    R.anim.enter,
                    R.anim.pop_enter,
                    R.anim.pop_exit,
                    R.anim.exit
                )
                .add(
                    R.id.fragment_container_view,
                    PaymentSummaryFragment.newInstance(bundle)
                )
                .commit()
        }
    }


    private fun initPurchaseOrderSummary() {
        lifecycleScope.launch {
            try {
                val purchaseAd = PurchaseOrderItemInput(
                    adId = intent.extras?.getString(Constants.VAS_AD_ID)!!,
                    quantity = 1
                )
                val purchaseSummary = PurchaseOrderSummaryInput(listOf(purchaseAd))
                val initQuery = withContext(Dispatchers.IO) {
                    apolloClient.query(
                        GetPurchaseOrderSummaryQuery(purchaseSummary)
                    ).execute()
                }

                if (initQuery.hasErrors()) {
                    binding.layoutError.root.visibility = View.VISIBLE
                }
                binding.layoutError.root.visibility = View.GONE
                ad = initQuery.data?.getPurchaseOrderSummary?.items?.get(0)?.ad
                val pricing =
                    initQuery.data?.getPurchaseOrderSummary?.items?.get(0)?.effectivePrice
                val shippingPrice = initQuery.data?.getPurchaseOrderSummary?.shippingFee
                paymentMethodsList =
                    initQuery.data?.getPurchaseOrderSummary?.availableECommercePaymentMethods
                setAdDetails(ad)
                setPriceDetails(pricing, shippingPrice)
                binding.continueButton.setOnClickListener {
                    intent.extras?.let { it1 ->
                        displaySummaryFragment(it1)
                    }
                }

                val analyticsPayload = HashMap<String?, String?>()
                ad?.adId?.let { analyticsPayload.put("ad_id", it) }
                ad?.price?.let {
                    analyticsPayload.put(
                        "ad_price",
                        it.withoutCurrency.toString()
                    )
                }
                intent.extras?.getString(Constants.SELLER_ID)?.let {
                    analyticsPayload.put("seller_id", it)
                }
                AnalyticsManager
                    .instance?.logEvent(
                        "checkout",
                        analyticsPayload,
                        true
                    )
            } catch (e: Exception) {
                binding.layoutError.root.visibility = View.VISIBLE
            }
        }
    }

    private suspend fun setAdDetails(ad: GetPurchaseOrderSummaryQuery.Ad?) {
        adId?.let { id ->
            val adQueryResult = apolloClient.query(GetEcommerceDetailsQuery(id)).execute()
            val stockAmount = adQueryResult.data?.getECommerceProductInfo?.itemsLeft
            with(binding) {
                stock.text = "${stockAmount.toString()} ${getString(R.string.stock_articles)}"
                stock2.text = "${stockAmount.toString()} ${getString(R.string.stock_articles)}"
                val image = ad?.media?.defaultImage?.paths?.smallThumbnail
                Glide.with(this@PaymentDetailsActivity).load(image)
                    .placeholder(R.drawable.ic_no_image_placeholder)
                    .into(adThumbView)
                Glide.with(this@PaymentDetailsActivity).load(image)
                    .placeholder(R.drawable.ic_no_image_placeholder)
                    .into(adThumbView2)
                val title = ad?.title
                adTitleText.text = title
                adTitleText2.text = title
            }
        }
    }

    private fun setPriceDetails(price: Int?, shippingFee: Int?) {
        with(binding) {
            subTotal.text = price.toString() + getString(R.string.currency)
            shipping.text = shippingFee.toString() + getString(R.string.currency)
            total.text =
                (shippingFee?.let { price?.plus(it) }).toString() + getString(R.string.currency)
            totalPrice.text =
                (shippingFee?.let { price?.plus(it) }).toString() + getString(R.string.currency)
        }
    }
}

fun ComponentActivity.applyEdgeToEdge(fittedViewId: Int) {
    val view: View = findViewById(fittedViewId)

    // Set Listener
    ViewCompat.setOnApplyWindowInsetsListener(view) { v, windowInsets ->
        val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
        // Apply the insets as a margin to the view. This solution sets
        // only the bottom, left, and right dimensions, but you can apply whichever
        // insets are appropriate to your layout. You can also update the view padding
        // if that's more appropriate.
        v.updateLayoutParams<MarginLayoutParams> {
            topMargin = insets.top
            leftMargin = insets.left
            bottomMargin = insets.bottom
            rightMargin = insets.right
        }

        // Return CONSUMED if you don't want the window insets to keep passing
        // down to descendant views.
        WindowInsetsCompat.CONSUMED
    }
}