package se.scmv.morocco.ecomerce.checkout

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import se.scmv.morocco.R
import se.scmv.morocco.databinding.OrderSuccessFragmentBinding
import se.scmv.morocco.ui.AppDeepLinks

const val CC_PAYMENT = "CC_PAYMENT"

class PaymentSuccessPage : AppCompatActivity() {

    private var _binding: OrderSuccessFragmentBinding? = null
    private val binding: OrderSuccessFragmentBinding
        get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = OrderSuccessFragmentBinding.inflate(layoutInflater)
        setContentView(_binding?.root)

        with(binding) {
            pitch.text = if (intent.getBooleanExtra(CC_PAYMENT, false)) {
                getString(R.string.order_thank_you_CC)
            } else {
                getString(R.string.order_thank_you_COD)
            }
            gotoBuy.setOnClickListener {
                val ecommerceIntent = Intent(
                    Intent.ACTION_VIEW,
                    (AppDeepLinks.LISTING + "?params=category%3D5010&delivery%3Dtrue").toUri()
                )
                startActivity(ecommerceIntent)
            }
            gotoMyOrders.setOnClickListener {
                val intent = Intent(
                    Intent.ACTION_VIEW,
                    AppDeepLinks.ACCOUNT_ORDERS.toUri()
                )
                startActivity(intent)
                finish()
            }
        }
    }
}