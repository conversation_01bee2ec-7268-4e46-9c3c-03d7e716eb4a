package se.scmv.morocco.services

import android.app.Service
import android.util.Log
import com.apollographql.apollo3.ApolloClient
import com.braze.push.BrazeFirebaseMessagingService
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import se.scmv.morocco.core.AvitoPushNotificationsService
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.messaging.common.Constants.FIREBASE_MESSAGING_TOKEN
import se.scmv.morocco.messaging.notifications.AvitoMessagingService
import se.scmv.morocco.messaging.utils.MessagingUtils
import se.scmv.morocco.utils.Utils
import java.lang.reflect.Field
import javax.inject.Inject

@AndroidEntryPoint
class FirebaseMessagingProxyService : FirebaseMessagingService() {

    @Inject
    lateinit var apolloClient: ApolloClient

    @Inject
    lateinit var accountRepository: AccountRepository

    companion object {
        //Solving android uninstall push notification not being silent
        const val AF_UNINSTALL_TRACKING = "af-uninstall-tracking"
    }

    private val messagingServices: List<FirebaseMessagingService> by lazy {
        listOf(
            AvitoPushNotificationsService(),
            AvitoMessagingService(),
            BrazeFirebaseMessagingService()
        ).onEach { it.injectContext(this) }
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        messagingServices.forEach { it.onNewToken(token) }
        val exceptionHandler = CoroutineExceptionHandler { _, error ->
            Log.e(
                "registerFirebaseToken",
                "Error: ${error.localizedMessage}"
            )
        }
        CoroutineScope(Dispatchers.IO + exceptionHandler).launch {
            val oldToken = Utils.getStringPreference(
                ctx = this@FirebaseMessagingProxyService,
                key = FIREBASE_MESSAGING_TOKEN
            )
            if (token != oldToken) {
                Utils.savePreference(
                    ctx = this@FirebaseMessagingProxyService,
                    key = FIREBASE_MESSAGING_TOKEN,
                    value = token
                )

                val account = accountRepository.currentAccount.firstOrNull()
                if (account != null && account.isLogged()){
                    MessagingUtils.registerFirebaseToken(
                        apolloClient = apolloClient,
                        context = this@FirebaseMessagingProxyService,
                        newToken = token,
                        oldToken = oldToken
                    )
                }
            }
        }
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        messagingServices.forEach {
            if (remoteMessage.data.containsKey(AF_UNINSTALL_TRACKING)) {
                return
            } else {
                it.onMessageReceived(remoteMessage)
            }
        }
    }

    override fun onDeletedMessages() {
        super.onDeletedMessages()
        messagingServices.forEach { it.onDeletedMessages() }
    }

    override fun onMessageSent(message: String) {
        super.onMessageSent(message)
        messagingServices.forEach { it.onMessageSent(message) }
    }

    override fun onSendError(message: String, e: Exception) {
        super.onSendError(message, e)
        messagingServices.forEach { it.onSendError(message, e) }
    }
}

fun <T : Service> T.injectContext(context: T, func: T.() -> Unit = {}) {
    setField("mBase", context)
    func()
}

fun Class<*>.findDeclaredField(name: String): Field? {
    var clazz: Class<*>? = this
    do {
        try {
            return clazz?.getDeclaredField(name)
        } catch (e: Throwable) {
        }
        clazz = clazz?.superclass
    } while (clazz != null)
    return null
}

fun Any.setField(name: String, value: Any): Boolean =
    javaClass.findDeclaredField(name)?.let {
        try {
            it.isAccessible = true
            it.set(this, value)
            true
        } catch (e: Throwable) {
            false
        }
    } ?: false

val Any.TAG: String
    get() {
        val tag = javaClass.simpleName
        val max = 23
        return if (tag.length <= max) tag else tag.substring(0, max)
    }