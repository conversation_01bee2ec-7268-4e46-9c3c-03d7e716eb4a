package se.scmv.morocco.network.util

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Build
import java.text.Normalizer
import java.util.Locale
import java.util.regex.Pattern

class UserAgent(private val context: Context) {
        private val userAgentString: String

        /**
         * Returns UserAgent string in format
         * "application_name/application_version (package_name; build:version_code; Android android_version; Model:device_model)"
         *
         * @return user agent string
         */
        fun getUserAgentString(): String {
                return deaccent(String.format("%s", userAgentString))
        }

        /**
         * Get device model eg. Nexus 5X, SM-A310F etc.
         *
         * @return String device model
         */
        private val model: String
                private get() = Build.MODEL

        /**
         * Get version of android as string eg. 4.1.2, 7.0.1 etc.
         *
         * @return String as android version
         */
        private val androidVersion: String
                private get() = Build.VERSION.RELEASE

        /**
         * Get application version code
         *
         *
         * If retrieving of version code fails for any reason, number 0 is returned as version code
         *
         * @param ctx application Context
         * @return integer as version code
         */
        private fun getVersionCode(ctx: Context): Int {
                return try {
                        ctx.packageManager.getPackageInfo(ctx.packageName, 0).versionCode
                } catch (e: PackageManager.NameNotFoundException) {
                        0
                }
        }

        /**
         * Get package name of application
         *
         * @param ctx application Context
         * @return String as package name
         */
        private fun getPackageName(ctx: Context): String {
                return ctx.packageName
        }

        /**
         * Get version name of application eg. 1.2.3
         *
         *
         * If retrieving of version names fails for any reason, string "Unknown" is returned as version name
         *
         * @param ctx application Context
         * @return String as version name
         */
        private fun getVersionName(ctx: Context): String {
                return try {
                        ctx.packageManager.getPackageInfo(ctx.packageName, 0).versionName ?: "Unknown"
                } catch (e: PackageManager.NameNotFoundException) {
                        "Unknown"
                }
        }

        /**
         * Get name of application that is defined in AndroidManifest by application entity
         *
         * @param context application Context
         * @return String app name
         */
        private fun getApplicationName(context: Context): String {
                val applicationInfo: ApplicationInfo = context.applicationInfo
                val stringId = applicationInfo.labelRes
                return if (stringId == 0) {
                        applicationInfo.nonLocalizedLabel?.toString() ?: "Avito"
                } else {
                        context.getString(stringId)
                }
        }


        /**
         * Removes all the diacritics from the source string
         *
         * @param source the string you want to remove the diacritics from
         * @return the source string without any diacritics
         */
        private fun deaccent(source: String): String {
                val nfdNormalizedString: String =
                        Normalizer.normalize(source, Normalizer.Form.NFD)
                val pattern: Pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+")
                return pattern.matcher(nfdNormalizedString).replaceAll("")
        }


        /**
         * Private constructor that generates and caches User-Agent string
         *
         * @param ctx application Context
         */
        init {
                userAgentString = java.lang.String.format(
                        Locale.FRENCH,
                        "%s/%s (%s; build:%d; Android %s; Model:%s)",
                        getApplicationName(context),
                        getVersionName(context),
                        getPackageName(context),
                        getVersionCode(context),
                        androidVersion,
                        model
                )
        }
}
