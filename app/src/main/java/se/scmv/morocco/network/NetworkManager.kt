package se.scmv.morocco.network

import android.content.Context
import android.net.ConnectivityManager


/**
 * Created by amine on 08/12/14.
 */
class NetworkManager(private val mContext: Context) {
    private val cm: ConnectivityManager = mContext.applicationContext.getSystemService(
        Context.CONNECTIVITY_SERVICE
    ) as ConnectivityManager

    //private static NetworkManager mInstance;
    var networkListener: NetworkStatusListener? = null

    val isConnected: Boolean
        /**
         * Get the availability of the Internet connection.
         *
         * @return true: Internet available
         * false: no Internet available
         */
        get() {
            if (this.isInternetAvailable) {
                if (networkListener != null) networkListener!!.onConnected()
                return true
            }

            val manager =
                mContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val netInfo = manager.getActiveNetworkInfo()
            if (netInfo != null && netInfo.isConnected()) {
                if (networkListener != null) networkListener!!.onConnected()
                return true
            } else {
                if (networkListener != null) networkListener!!.onUnavailableNetwork()
                return false
            }
        }

    private val isInternetAvailable: Boolean
        get() {
            val activeNetwork = cm.getActiveNetworkInfo()
            return activeNetwork != null && activeNetwork.isConnectedOrConnecting()
        }

    interface NetworkStatusListener {
        fun onConnected()

        fun onUnavailableNetwork()
    }
}
