package se.scmv.morocco.analytics

import android.content.Context
import android.os.Bundle
import androidx.core.os.bundleOf
import com.braze.Braze
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.analytics.FirebaseAnalytics
import se.scmv.morocco.common.lang.LocaleManager.getCurrentLanguage
import se.scmv.morocco.utils.Utils
import java.util.Locale


class AnalyticsManager private constructor(private val mContext: Context) {
    private val braze: Braze
    private val facebookLogger: AppEventsLogger
    private val mFirebaseAnalytics: FirebaseAnalytics

    init {
        braze = Braze.getInstance(mContext)
        facebookLogger = AppEventsLogger.newLogger(mContext)
        mFirebaseAnalytics = FirebaseAnalytics.getInstance(mContext)
    }

    /**
     * This method fire the given event to analytics platform amplitude for both avito and tayara
     * -Appboy for avito
     *
     * @param event           event
     * @param eventProperties list of properties
     * @param logFirebase
     */
    //todo: (in firebase tagging plan tasks) this methode and logFirebase do the same thing.
    fun logEvent(
        event: String,
        eventProperties: Map<String?, String?>,
        logFirebase: Boolean
    ) {
        val updatedProperties = eventProperties.toMutableMap()
        updatedProperties[USER_LANG] = getCurrentLanguage()
        if (logFirebase) {
            logFirebaseEvent(event, updatedProperties.toMap()) // Convert back to immutable map
        }
    }

    fun logFirebaseEvent(event: String, eventProperties: Map<String?, String?>) {
        mFirebaseAnalytics.logEvent(event.replace(" ", "_"), getBundleFrom(eventProperties))
    }

    private fun getBundleFrom(properties: Map<String?, String?>): Bundle {
        val bundleProperties = Bundle()
        for (key in properties.keys) {
            if (properties[key] != null && properties[key]!!.isNotEmpty()) {
                bundleProperties.putString(key?.trim { it <= ' ' }
                    ?.replace(" ", "_")?.replace("/", "_")?.replace("-", "_")?.replace("'", "_")
                    ?.lowercase(
                        Locale.getDefault()
                    ), properties[key]!!
                    .lowercase(Locale.getDefault()))
            }
        }
        return bundleProperties
    }

    fun logVasEvent(context: Context, event: String, eventProperties: Bundle) {
        FirebaseAnalytics.getInstance(context).logEvent(event, eventProperties)
    }


    /**
     * This function is dedicated to track VAS events without properties
     *
     * @param event
     */
    fun logVasEvent(context: Context, event: String) {
        FirebaseAnalytics.getInstance(context).logEvent(event, bundleOf())
    }

    /**
     * This function is for pushing a screen using the AnalyticsManager instance
     */
    fun pushScreen(context: Context, screenName: String) {
        FirebaseAnalytics.getInstance(context).logEvent(
            "screenView",
            bundleOf("screenName" to screenName)
        )
    }

    fun setUserId(userProperties: Map<String?, String?>?) {
        if (!Utils.getBooleanPreference(mContext, PREF_IS_IDENTIFIED, false)) {
            if (userProperties != null) {
                if (userProperties[EMAIL_ATTRIBUTE] != null) {
                    braze.changeUser(
                        userProperties[EMAIL_ATTRIBUTE]!!.lowercase(Locale.getDefault())
                    )
                }
                val currentUser = braze.currentUser
                if (currentUser != null) {
                    currentUser.setFirstName(userProperties[NAME_ATTRIBUTE])
                    currentUser.setPhoneNumber(userProperties[PHONE_ATTRIBUTE])
                    currentUser.setCustomUserAttribute(USER_LANG, getCurrentLanguage())
                }
                Utils.savePreference(mContext, PREF_IS_IDENTIFIED, true)
            }
        }
    }

    fun unSetUserId() {
        Utils.savePreference(mContext, PREF_IS_IDENTIFIED, false)
    }

    companion object {
        const val USER_LANG = "Lang"
        const val IS_STORE_ACCOUNT = "IsStoreAccount"
        const val NAME_ATTRIBUTE = "Name"
        const val EMAIL_ATTRIBUTE = "Email"
        const val PHONE_ATTRIBUTE = "Phone"
        private const val PREF_IS_IDENTIFIED = "is_identified"
        var instance: AnalyticsManager? = null
            private set

        fun initialize(context: Context): AnalyticsManager? {
            if (instance == null) instance = AnalyticsManager(context)
            return instance
        }
    }
}