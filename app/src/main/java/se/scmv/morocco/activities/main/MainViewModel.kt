package se.scmv.morocco.activities.main

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val configRepository: ConfigRepository,
    accountRepository: AccountRepository
) : ViewModel() {

    val viewState: StateFlow<MainViewState> = accountRepository.currentAccount.map {
        MainViewState.Success(it)
    }.stateIn(
        scope = viewModelScope,
        initialValue = MainViewState.Loading,
        started = SharingStarted.WhileSubscribed(5_000),
    )

    init {
        viewModelScope.launch {
            configRepository.refresh()
        }
    }

    // TODO Should find a way to initialize the account from the shared preferences token for old users.
    private suspend fun checkIfPreConnected() {

    }
}

