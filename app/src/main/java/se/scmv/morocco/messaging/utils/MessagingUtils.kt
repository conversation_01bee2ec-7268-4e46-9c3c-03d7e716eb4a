package se.scmv.morocco.messaging.utils

import android.content.Context
import android.util.Log
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import se.scmv.morocco.RegisterFirebaseTokenMutation
import se.scmv.morocco.messaging.common.Constants
import se.scmv.morocco.type.DevicePlatform
import se.scmv.morocco.type.FirebaseTokenInput
import se.scmv.morocco.utils.Utils
import java.net.SocketTimeoutException

object MessagingUtils {

    @JvmStatic
    fun registerFirebaseToken(
        apolloClient: ApolloClient,
        context: Context,
        newToken: String,
        oldToken: String? = null,
    ) {
        try {
            val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
            val errorHandler = CoroutineExceptionHandler { _, error ->
                Log.e(
                    "registerFirebaseToken",
                    "Error: ${error.localizedMessage}"
                )
            }
            coroutineScope.launch(errorHandler) {
                val token = FirebaseTokenInput(
                    platform = DevicePlatform.ANDROID,
                    new = Optional.presentIfNotNull(listOf(newToken)),
                    revoked = oldToken?.let {
                        Optional.presentIfNotNull(listOf(it))
                    } ?: Optional.Absent
                )

                apolloClient.mutation(RegisterFirebaseTokenMutation(token))
                    .toFlow()
                    .collect {
                        Utils.savePreference(
                            context,
                            Constants.FIREBASE_MESSAGING_TOKEN, newToken
                        )
                    }
            }
        } catch (se: SocketTimeoutException) {
            Log.e("registerFirebaseToken", "Error: ${se.message}")
        } catch (ex: Throwable) {
            Log.e("registerFirebaseToken", "Error ${ex.message}")
        }
    }

    @JvmStatic
    fun unRegisterFirebaseToken(
        apolloClient: ApolloClient,
        context: Context,
        oldToken: String
    ) {
        try {
            val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
            val errorHandler = CoroutineExceptionHandler { _, error ->
                Log.e(
                    "unRegisterFirebaseToken",
                    "Error: ${error.localizedMessage}"
                )
            }
            coroutineScope.launch(errorHandler) {
                val token = FirebaseTokenInput(
                    platform = DevicePlatform.ANDROID,
                    new = Optional.Absent,
                    revoked = Optional.presentIfNotNull(listOf(oldToken))
                )

                apolloClient.mutation(RegisterFirebaseTokenMutation(token))
                    .toFlow()
                    .collect {
                        Utils.removePreference(
                            context,
                            Constants.FIREBASE_MESSAGING_TOKEN
                        )
                    }
            }
        } catch (se: SocketTimeoutException) {
            Log.e("registerFirebaseToken", "Error: ${se.message}")
        } catch (ex: Throwable) {
            Log.e("registerFirebaseToken", "Error ${ex.message}")
        }
    }
}