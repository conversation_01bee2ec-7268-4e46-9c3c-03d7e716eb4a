package se.scmv.morocco.messaging.common

object Constants {


        //Tagging event properties
        const val ACCOUNT_TYPE_SHOP = "pro"
        const val ACCOUNT_TYPE_PRIVATE = "private"
        const val ELEMENT_CLICKED = "element_clicked"
        const val ELEMENT_NAME = "element_name"
        const val SELLER_TYPE = "seller_type"
        const val PAGE_NAME = "page_name"


        //prefs
        const val FIREBASE_MESSAGING_TOKEN = "firebase_messaging_token"
        const val CURRENTLY_OPENED_CONVERSATION_ID = "currently_opened_conversation_id"

        //push notification
        const val NOTIFICATION_CHANNEL_ID = "se.scmv.morocco.notification.DIRECT_MESSAGES"
        const val NOTIFICATION_ID = 101
        const val MESSAGING_NOTIFICATION_TYPE = "new_chat_message"
}