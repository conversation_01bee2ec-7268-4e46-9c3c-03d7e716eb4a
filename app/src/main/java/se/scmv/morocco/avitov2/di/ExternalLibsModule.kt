package se.scmv.morocco.avitov2.di

import com.google.firebase.messaging.FirebaseMessaging
import com.google.gson.Gson
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.BuildConfig
import se.scmv.morocco.account.presentation.master.AppConfig
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object ExternalLibsModule {

    @Provides
    @Singleton
    fun provideGson(): Gson = Gson()

    @Provides
    @Singleton
    fun provideFirebaseMessaging(): FirebaseMessaging = FirebaseMessaging.getInstance()

    @Provides
    @Singleton
    fun provideAppConfig(): AppConfig = object : AppConfig {
        override val versionName: String = BuildConfig.VERSION_NAME
        override val versionCode: Int = BuildConfig.VERSION_CODE
    }
}