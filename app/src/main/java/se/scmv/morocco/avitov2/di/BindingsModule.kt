package se.scmv.morocco.avitov2.di

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.adstickybanner.repositories.AdServerRepository
import se.scmv.morocco.adstickybanner.repositories.AdServerRepositoryImpl

@Module
@InstallIn(SingletonComponent::class)
abstract class BindingsModule {

    @Binds
    abstract fun bindAdServerRepository(
        adServerRepositoryImpl: AdServerRepositoryImpl
    ): AdServerRepository
}