package se.scmv.morocco.avitov2.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.domain.navigation.AppAction
import se.scmv.morocco.domain.navigation.EcommerceNavigator
import se.scmv.morocco.navigation.AppActionImpl
import se.scmv.morocco.navigation.EcommerceNavigatorImpl

@Module
@InstallIn(SingletonComponent::class)
object NavigationModule {

    @Provides
    fun provideAppAction(
        @ApplicationContext context: Context
    ): AppAction{
        return AppActionImpl(context)
    }

    @Provides
    fun provideEcommerceNavigator( @ApplicationContext context: Context): EcommerceNavigator {
        return EcommerceNavigatorImpl(context)
    }
}