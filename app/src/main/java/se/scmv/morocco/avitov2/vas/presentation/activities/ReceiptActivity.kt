package se.scmv.morocco.avitov2.vas.presentation.activities

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import pub.devrel.easypermissions.EasyPermissions
import se.scmv.morocco.R
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.databinding.ActivityReceiptBinding
import se.scmv.morocco.ecomerce.checkout.applyEdgeToEdge
import se.scmv.morocco.utils.Constants
import se.scmv.morocco.utils.DateUtils
import se.scmv.morocco.utils.ImageUtils

class ReceiptActivity : AppCompatActivity(), EasyPermissions.PermissionCallbacks {

    private var _binding: ActivityReceiptBinding? = null
    private val binding: ActivityReceiptBinding
        get() = _binding!!

    var extras: Bundle? = null
    private var toolbar: Toolbar? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityReceiptBinding.inflate(layoutInflater)
        setContentView(_binding?.root)
        applyEdgeToEdge(R.id.root)
        // Set toolbar
        setToolbar()

        // Get extras
        extras = intent.extras
        if (extras != null) {
            fillInTheReceipt()
        }
        setUpTheLoggedInUI()
        //Push Receipt Screen
        AnalyticsManager.instance?.pushScreen(this, "VasReceiptScreen")
    }

    private fun setToolbar() {
        toolbar = binding.toolbar.toolbar
        toolbar?.setTitle(R.string.vas_receipt_activity_title)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(false)
        toolbar?.setNavigationIcon(R.drawable.ic_view_close)
    }

    private fun setUpTheLoggedInUI() {

    }

    private fun fillInTheReceipt() {
        with(binding) {
            val res = resources
            val email = res.getString(
                R.string.vas_receipt_email_sent_message,
                extras?.getString(Constants.CREDIT_CARD_EMAIL)
            )
            receiptSentEmailMessage.text = email
            receiptDate.text = DateUtils.getCurrentDateWithFormat(
                format = DateUtils.DATE_FORMAT_YEAR_MONTH_DAY
            )
            receiptReceiverName.text = String.format(
                "%s %s",
                extras?.getString(Constants.CREDIT_CARD_FIRST_NAME),
                extras?.getString(Constants.CREDIT_CARD_LAST_NAME)
            )
            receiptTransactionNumber.text = extras?.getString(Constants.VAS_PAYMENT_KEY)
            receiptPaymentMethod.text = BANK_CARD
            receiptOrderTitle.text = extras?.getString(Constants.VAS_UNIT_TITLE)
            receiptOrderDuration.text = extras?.getString(Constants.VAS_UNIT_DURATION)
            val price = extras?.getString(Constants.VAS_UNIT_NORMAL_PRICE)
            receiptPackagePrice.text = price
            val totalPrice = res.getString(R.string.vas_payment_total, price)
            receiptTotal.text = totalPrice
        }
    }

    /**
     * Go to the listing
     *
     * @param view
     */
    fun goToListing(view: View?) {
        startListingActivity()
    }

    private fun notifyMainActivity() {

    }

    /**
     * This function contain the logic behind taking a screenshot
     */
    private fun saveVasCodeAsScreenShot() {
        //Take crash Payment Code
        trackScreenshotCTAAnalytic()
        //parentView
        val rootView = findViewById<View>(R.id.receipt_container)
        //Bitmap
        val screenShotBitmap = ImageUtils.getViewContentAsBitmap(rootView)
    }

    /**
     * Take the ScreenShoot
     *
     * @param view
     */
    fun tackScreensShot(view: View?) {
        val perms: Array<String> = arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        if (EasyPermissions.hasPermissions(
                this@ReceiptActivity,
                *perms
            ) || Build.VERSION.SDK_INT >= Build.VERSION_CODES.R
        ) {
            saveVasCodeAsScreenShot()
        } else {
            EasyPermissions.requestPermissions(
                this@ReceiptActivity,
                getString(R.string.common_storage_permissions_required),
                MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE,
                *perms
            )
        }
    }

    /**
     * check if the app have the write permission
     *
     * @return
     */
    val isWritePermissionGranted: Int
        get() = ContextCompat.checkSelfPermission(
            this@ReceiptActivity,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )

    /**
     * This function's for starting a new listing Activity
     */
    private fun startListingActivity() {
        trackListNavigationAnalytic()
        val listingIntent = Intent(this@ReceiptActivity, MainActivity::class.java)
        startActivity(listingIntent)
        finish()
    }

    /**
     * Function for requesting the write permission
     */
    private fun requestWritePermission() {

        // Here, thisActivity is the current activity
        if (ContextCompat.checkSelfPermission(
                this@ReceiptActivity,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
            != PackageManager.PERMISSION_GRANTED
        ) {
            ActivityCompat.requestPermissions(
                this@ReceiptActivity,
                arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        EasyPermissions.onRequestPermissionsResult(
            requestCode,
            permissions,
            grantResults,
            this
        )
    }

    override fun onPermissionsGranted(requestCode: Int, perms: MutableList<String>) {
        when (requestCode) {
            MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE -> saveVasCodeAsScreenShot()
        }
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) {
        when (requestCode) {
            MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE -> Toast.makeText(
                this@ReceiptActivity, getString(R.string.common_storage_permissions_required),
                Toast.LENGTH_LONG
            ).show()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        val id = item.itemId
        if (id == android.R.id.home) {
            startListingActivity()
        }
        return super.onOptionsItemSelected(item)
    }

    /**
     * track payment method selection event
     */
    private fun trackListNavigationAnalytic() {
        val manager = AnalyticsManager.instance
        if (manager != null) {
            val properties = bundleOf(
                "Source"
                        to "Credit card Payment Success Page"
            )
            manager.logVasEvent(
                this@ReceiptActivity,
                "ReturnListingPage",
                properties
            )
        }
    }

    /**
     * track filled form event
     */
    private fun trackScreenshotCTAAnalytic() {
        val manager = AnalyticsManager.instance
        manager?.logVasEvent(this, "Took Receipt Screenshot")
    }

    companion object {
        const val BANK_CARD = "Carte Bancaire"
        const val MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE = 19
    }
}