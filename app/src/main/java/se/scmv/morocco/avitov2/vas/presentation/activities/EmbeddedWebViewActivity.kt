package se.scmv.morocco.avitov2.vas.presentation.activities

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ProgressBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import se.scmv.morocco.R
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.ecomerce.checkout.CC_PAYMENT
import se.scmv.morocco.ecomerce.checkout.PaymentSuccessPage
import se.scmv.morocco.ecomerce.checkout.applyEdgeToEdge

class EmbeddedWebViewActivity : AppCompatActivity() {

    // Loading indicator
    private var mProgressIndicator: ProgressBar? = null

    // Error message text view
    private var mErrorText: TextView? = null

    // Web view
    private var mWebView: WebView? = null
    private var paymentURL: String? = null
    private var toolbar: Toolbar? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_credit_card_init)
        applyEdgeToEdge(R.id.root)
        mProgressIndicator = findViewById(R.id.progress_indicator)
        initToolBar()
        initWebView()
        fillInTheWebView()
    }

    fun initToolBar() {
        toolbar = findViewById(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(true)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }

    private fun initWebView() {
        // Load payzone page in web view
        mWebView = findViewById(R.id.payzone_webview)
        mWebView?.webViewClient = MyWebViewClient()

        // Web view settings
        val webSettings = mWebView?.settings

        // Web view scaling related parameters
        webSettings?.loadWithOverviewMode = true
        webSettings?.useWideViewPort = true
        mWebView?.setInitialScale(1)

        // Web view zoom related parameters
        webSettings?.builtInZoomControls = true
        webSettings?.displayZoomControls = false
        webSettings?.setSupportZoom(true)

        // Enable javascript in the web view
        webSettings?.javaScriptEnabled = true
    }

    private fun fillInTheWebView() {
        // If we are able to get the merchant code, we display the payzone page to the user
        if (extrasAreValid()) {
            when (intent.extras?.getString(WEBVIEW_CONTENT_TYPE)) {
                MONETISATION -> initMonetisationWebView()
                MEDIA -> initMediaWebView()
                ECOMMERCE -> initEcommerceWebView()
            }
        } else {
            displayOnErrorUi()
        }
    }

    private fun initEcommerceWebView() {

        title = getString(R.string.webview_title_credit_card_payment)
        // Get customer key for payzone url
        val customerToken =
            intent.extras?.getString(CUSTOMER_TOKEN)
        // Web view onclick event test
        mWebView?.addJavascriptInterface(WebViewInterface(this), "paymentObject")

        paymentURL = intent.extras?.getString(ECOMMERCE_PAYMENT_URL)
        // Load url in web view
        if (paymentURL != null) {
            mWebView?.loadUrl(paymentURL!!)
        }

        //Push Credit Card payment Screen
        AnalyticsManager.instance?.pushScreen(
            this,
            "EcommerceCCPaymentScreen"
        )
    }

    private fun extrasAreValid(): Boolean {
        return intent != null && intent.extras != null && intent.extras?.containsKey(
            WEBVIEW_CONTENT_TYPE
        ) == true
    }

    private fun initMediaWebView() {
        if (intent.extras != null && intent.extras?.containsKey(WEBVIEW_URL) == true) {
            intent.extras?.getString(WEBVIEW_URL)?.let { mWebView?.loadUrl(it) }
            title = intent.extras?.getString(MEDIA_COMPANY_NAME)
        } else {
            displayOnErrorUi()
        }
        //Push Credit Card payment Screen
        AnalyticsManager.instance
            ?.pushScreen(this, "MediaWebViewScreen")
    }

    private fun initMonetisationWebView() {
        if (intent.extras?.containsKey(CUSTOMER_TOKEN) == true) {
            title = getString(R.string.webview_title_credit_card_payment)
            // Get customer key for payzone url
            val customerToken =
                intent.extras?.getString(CUSTOMER_TOKEN)
            // Web view onclick event test
            mWebView?.addJavascriptInterface(WebViewInterface(this), "paymentObject")

            // Load url in web view
            customerToken?.let {
                mWebView?.loadUrl(it)
            }
        } else {
            displayOnErrorUi()
        }

        //Push Credit Card payment Screen
        AnalyticsManager.instance
            ?.pushScreen(this, "VasCCPaymentScreen")
    }

    private fun displayOnErrorUi() {
        mWebView?.visibility = View.GONE
        mErrorText = findViewById(R.id.error_message)
        mErrorText?.visibility = View.VISIBLE
    }

    /**
     * Web view interface with javascript
     * Allows to handle the button click once the user is done
     * with the credit card payment
     */
    inner class WebViewInterface internal constructor(var context: Context) {
        @JavascriptInterface
        fun quitCreditCardPayment() {
            trackFilledPageCCAnalytic()
            callTheReceiptView()
        }
    }

    /**
     * Web view client class created to handle page navigation
     */
    private inner class MyWebViewClient : WebViewClient() {
        override fun onPageFinished(view: WebView, url: String) {
            showProgressIndicator(false)
        }
    }

    fun callTheReceiptView() {
        if (paymentURL != null) {
            val mainActivity =
                Intent(this@EmbeddedWebViewActivity, PaymentSuccessPage::class.java)
            intent.extras?.let { mainActivity.putExtras(it) }
            mainActivity.putExtra(CC_PAYMENT, true)
            startActivity(mainActivity)
            finish()
        } else {
            val mainActivity =
                Intent(this@EmbeddedWebViewActivity, ReceiptActivity::class.java)
            intent.extras?.let { mainActivity.putExtras(it) }
            startActivity(mainActivity)
            finish()
        }

    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        return super.onCreateOptionsMenu(menu)
    }

    /**
     * @param showProgressIndicator: When true, display the progress indicator,
     * when false, hide it
     */
    private fun showProgressIndicator(showProgressIndicator: Boolean) {
        if (mProgressIndicator != null) mProgressIndicator?.visibility =
            if (showProgressIndicator) View.VISIBLE else View.GONE
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return super.onOptionsItemSelected(item)
    }

    /**
     * track Send the filled payment page credit card event
     */
    private fun trackFilledPageCCAnalytic() {

        val manager = AnalyticsManager.instance
        manager?.logVasEvent(this, "Sent Payzone WebWiew Form")
    }

    companion object {
        const val WEBVIEW_CONTENT_TYPE = "webview_content_type"
        const val WEBVIEW_URL = "webview_url"
        const val MEDIA_COMPANY_NAME = "media_company_name"
        const val MEDIA = "media"
        const val MONETISATION = "monetisation"
        const val ECOMMERCE = "ECOMMERCE"
        const val ECOMMERCE_PAYMENT_URL = "ECOMMERCE_PAYMENT_URL"
        const val CUSTOMER_TOKEN = "customer_token"
    }
}