<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:background="@color/appBackground">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/appBackground"
        android:clipToPadding="false"
        android:paddingBottom="170dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/appBackground">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/payment_Details_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/appBackground"
                android:visibility="visible">

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/formContainer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="parent">

                    <TextView
                        android:id="@+id/shippingDetailsLabel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_default"
                        android:layout_marginTop="32dp"
                        android:fontFamily="@font/rubik_medium"
                        android:gravity="center_vertical"
                        android:text="@string/shipping_title"
                        android:textColor="@color/orionBlack"
                        android:textSize="20sp" />

                    <TextView
                        android:id="@+id/shippingDetails"
                        style="@style/OrionTextExtra"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:fontFamily="@font/rubik"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/margin_default"
                        android:paddingEnd="@dimen/margin_default"
                        android:text="@string/shipping_pitch"
                        android:textColor="@color/orionBlack"
                        android:textSize="16sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/shippingDetailsLabel" />

                    <ma.avito.orion.ui.input.edittext.OrionEditText
                        android:id="@+id/fullNameField"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_default"
                        android:layout_marginTop="@dimen/margin_big"
                        android:layout_marginEnd="@dimen/margin_default"
                        android:maxLines="1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/shippingDetails"
                        app:orionHint="@string/full_name"
                        app:orionIsRequired="true"
                        app:orionStartIcon="@drawable/ic_user_line"
                        app:orionTitle="@string/full_name"
                        app:orionValidationMessage="@string/invalid_name"
                        app:orionValidationRexeg="@string/regex_minimum_two_characters"/>

                    <ma.avito.orion.ui.input.edittext.OrionEditText
                        android:id="@+id/accountEmailField"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_default"
                        android:layout_marginTop="@dimen/margin_big"
                        android:layout_marginEnd="@dimen/margin_default"
                        android:maxLines="1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/fullNameField"
                        app:orionHint="@string/email"
                        app:orionIsRequired="true"
                        app:orionStartIcon="@drawable/ic_email_outline"
                        app:orionTitle="@string/email"
                        app:orionValidationMessage="@string/error_invalid_email"
                        app:orionValidationRexeg="@string/regex_email"

                        />

                    <ma.avito.orion.ui.input.edittext.OrionEditText
                        android:id="@+id/phoneField"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_default"
                        android:layout_marginTop="@dimen/margin_big"
                        android:layout_marginEnd="@dimen/margin_default"
                        android:maxLines="1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/accountEmailField"
                        app:orionHint="@string/phone"
                        app:orionIsRequired="true"
                        app:orionStartIcon="@drawable/ic_phone_outline"
                        app:orionTitle="@string/phone"
                        app:orionValidationMessage="@string/phone_verification_invalid_phone"
                        app:orionValidationRexeg="@string/regex_phone_number"/>

                    <ma.avito.orion.ui.input.edittext.OrionEditText
                        android:id="@+id/deliveryAddressField"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_default"
                        android:layout_marginTop="@dimen/margin_big"
                        android:layout_marginEnd="@dimen/margin_default"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/phoneField"
                        app:orionAction="clear"
                        app:orionHint="Adresse de livraison"
                        app:orionIsLarge="true"
                        app:orionIsRequired="true"
                        app:orionIsValidationOptional="false"
                        app:orionMaxLength="200"
                        app:orionTitle="@string/your_delivery_address" />

                </androidx.appcompat.widget.LinearLayoutCompat>


            </androidx.constraintlayout.widget.ConstraintLayout>


        </RelativeLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>