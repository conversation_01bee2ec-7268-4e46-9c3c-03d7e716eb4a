<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/gotoMyOrders"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image"
            android:layout_width="170dp"
            android:layout_height="170dp"
            android:src="@drawable/ic_confirm_icon_order"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/productsInDelivery"
            style="@style/OrionTextBigBoldDark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="15dp"
            android:text="@string/order_delivering_COD"
            android:textAlignment="center"
            app:layout_constraintTop_toBottomOf="@id/image" />

        <TextView
            android:id="@+id/pitch"
            style="@style/OrionText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:paddingStart="40dp"
            android:paddingEnd="40dp"
            android:text="@string/order_thank_you_COD"
            android:textAlignment="center" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <Button
        android:id="@+id/gotoBuy"
        style="@style/OrionButtonIconPrimary"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="18dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="18dp"
        android:layout_marginBottom="50dp"
        android:text="@string/continueOrders"
        android:textColor="#1877F2"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/gotoMyOrders"
        style="@style/OrionButtonPrimary"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="18dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="18dp"
        android:text="@string/see_my_orders"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/gotoBuy"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>