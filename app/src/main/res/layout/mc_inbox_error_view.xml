<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/linearLayout5"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:gravity="center_horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/mcTitleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/common_oups"
            app:layout_constraintBottom_toTopOf="@+id/text_view_description"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_view_description"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:text="@string/common_unexpected_error_verify_and_try_later"
            app:layout_constraintBottom_toTopOf="@+id/imageView4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mcTitleText" />

        <ImageView
            android:id="@+id/imageView4"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:contentDescription="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_view_description"
            app:srcCompat="@drawable/img_unexpected_error" />

        <Button
            android:id="@+id/retryButton"
            android:layout_width="208dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="64dp"
            android:clickable="false"
            android:text="@string/common_refresh"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView4"
            app:layout_constraintVertical_bias="0.0" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>