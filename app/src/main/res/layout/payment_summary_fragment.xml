<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/appBackground">

    <ScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/appBackground"
        android:clipToPadding="false"
        android:paddingBottom="170dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/payment_Details_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/appBackground"
            android:orientation="vertical"
            android:visibility="visible">

            <LinearLayout
                android:id="@+id/summaryContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="16dp"
                android:paddingBottom="16dp"
                android:visibility="visible">

                <TextView
                    android:id="@+id/recap"
                    style="@style/OrionTextExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="@string/summary_recap"
                    android:textColor="@color/orionGrey"
                    android:textSize="20sp" />

                <TextView
                    android:id="@+id/summaryNameLabel"
                    style="@style/OrionTextExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="@string/name_and_surname"
                    android:textColor="@color/orionGrey"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/summaryName"
                    style="@style/OrionTextExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="99,000 DH"
                    android:textColor="@color/orionBlack"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/summaryAddressLabel"
                    style="@style/OrionTextExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="22dp"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="@string/delivery_address"
                    android:textColor="@color/orionGrey"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/summaryAddress"
                    style="@style/OrionTextExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="99,000 DH"
                    android:textColor="@color/orionBlack"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/summaryMailLabel"
                    style="@style/OrionTextExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="22dp"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="@string/address_mail"
                    android:textColor="@color/orionGrey"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/summaryMail"
                    style="@style/OrionTextExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="99,000 DH"
                    android:textColor="@color/orionBlack"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/summaryPhoneLabel"
                    style="@style/OrionTextExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="22dp"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="@string/phone"
                    android:textColor="@color/orionGrey"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/summaryPhone"
                    style="@style/OrionTextExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="99,000 DH"
                    android:textColor="@color/orionBlack"
                    android:textSize="15sp" />

            </LinearLayout>

            <View
                android:id="@+id/dividerViewThree"
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_marginTop="22dp"
                android:background="@color/grey_line" />

            <LinearLayout
                android:id="@+id/paymentMethodsContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/payment_method_label"
                    style="@style/OrionInputTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_default"
                    android:layout_marginTop="@dimen/margin_normal"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:layout_marginBottom="@dimen/margin_normal"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:text="@string/payment_methods"
                    android:textSize="20sp"
                    android:visibility="visible" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/payment_methods"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:divider="@color/appBackground"
                    android:dividerHeight="10dp" />

            </LinearLayout>


        </LinearLayout>
    </ScrollView>
</RelativeLayout>
