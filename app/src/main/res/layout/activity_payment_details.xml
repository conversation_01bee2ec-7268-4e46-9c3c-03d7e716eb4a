<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ecomerce.checkout.PaymentDetailsActivity">


    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/nestedScrollView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/payContainer"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:background="@color/appBackground"
                android:clipToPadding="false"
                android:paddingBottom="20dp">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/appBackground">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/payment_Details_container"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/appBackground"
                        android:visibility="visible">

                        <ImageView
                            android:id="@+id/ad_thumb_view"
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:layout_marginStart="@dimen/margin_default"
                            android:layout_marginTop="24dp"
                            android:background="#F2F2F2"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_no_picture" />

                        <ImageView
                            android:id="@+id/stockIcon"
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:layout_marginStart="@dimen/margin_default"
                            android:layout_marginTop="34dp"
                            app:layout_constraintStart_toEndOf="@+id/ad_thumb_view"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_stock"
                            app:tint="@color/red_stats_color" />

                        <TextView
                            android:id="@+id/stock"
                            style="@style/OrionTextExtra"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="34dp"
                            android:fontFamily="@font/rubik"
                            android:gravity="center_vertical"
                            android:text="10 Article en stock"
                            android:textColor="@color/orionBlack"
                            android:textSize="14sp"
                            app:layout_constraintStart_toEndOf="@+id/stockIcon"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/adTitleText"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin_default"
                            android:layout_marginTop="@dimen/tiny_spacing"
                            android:layout_marginEnd="16dp"
                            android:fontFamily="@font/rubik"
                            android:gravity="center_vertical"
                            android:text="Fiat 500 Essence"
                            android:textColor="@color/orionBlack"
                            android:textSize="16sp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/ad_thumb_view"
                            app:layout_constraintTop_toBottomOf="@+id/stockIcon" />

                        <View
                            android:id="@+id/dividerViewOne"
                            android:layout_width="322dp"
                            android:layout_height="1dp"
                            android:layout_marginTop="28dp"
                            android:background="@color/grey_line"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/adTitleText" />

                        <TextView
                            android:id="@+id/subtotalLabel"
                            style="@style/OrionTextExtra"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin_default"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/rubik"
                            android:gravity="center_vertical"
                            android:text="@string/sub_fee"
                            android:textColor="@color/orionBlack"
                            android:textSize="16sp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/dividerViewOne" />

                        <TextView
                            android:id="@+id/subTotal"
                            style="@style/OrionTextExtra"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginTop="15dp"
                            android:layout_marginEnd="@dimen/margin_default"
                            android:fontFamily="@font/rubik_medium"
                            android:gravity="center_vertical"
                            android:text="99,000 DH"
                            android:textColor="@color/orionBlack"
                            android:textSize="16sp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="1"
                            app:layout_constraintStart_toEndOf="@+id/subtotalLabel"
                            app:layout_constraintTop_toBottomOf="@+id/dividerViewOne" />

                        <TextView
                            android:id="@+id/shippingLabel"
                            style="@style/OrionTextExtra"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin_default"
                            android:layout_marginTop="12dp"
                            android:fontFamily="@font/rubik"
                            android:gravity="center_vertical"
                            android:text="@string/shipping_fees"
                            android:textColor="@color/orionBlack"
                            android:textSize="16sp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/subtotalLabel" />

                        <TextView
                            android:id="@+id/shipping"
                            style="@style/OrionTextExtra"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginTop="12dp"
                            android:layout_marginEnd="@dimen/margin_default"
                            android:fontFamily="@font/rubik_medium"
                            android:gravity="center_vertical"
                            android:text="0,00 DH"
                            android:textColor="@color/orionBlack"
                            android:textSize="16sp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="1"
                            app:layout_constraintStart_toEndOf="@+id/shippingLabel"
                            app:layout_constraintTop_toBottomOf="@+id/subTotal" />

                        <View
                            android:id="@+id/dividerViewTwo"
                            android:layout_width="322dp"
                            android:layout_height="1dp"
                            android:layout_marginTop="28dp"
                            android:background="@color/grey_line"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/shipping" />


                        <TextView
                            android:id="@+id/totalLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin_default"
                            android:layout_marginTop="12dp"
                            android:fontFamily="@font/rubik_medium"
                            android:gravity="center_vertical"
                            android:text="@string/big_total"
                            android:textColor="@color/orionBlack"
                            android:textSize="18sp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/dividerViewTwo" />


                        <TextView
                            android:id="@+id/total"
                            style="@style/OrionTextExtra"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginTop="12dp"
                            android:layout_marginEnd="@dimen/margin_default"
                            android:fontFamily="@font/rubik_medium"
                            android:gravity="center_vertical"
                            android:text="99,000 DH"
                            android:textColor="@color/orionBlue"
                            android:textSize="19sp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="1"
                            app:layout_constraintStart_toEndOf="@+id/totalLabel"
                            app:layout_constraintTop_toBottomOf="@+id/dividerViewTwo" />

                        <View
                            android:id="@+id/dividerViewThree"
                            android:layout_width="match_parent"
                            android:layout_height="5dp"
                            android:layout_marginTop="15dp"
                            android:background="@color/grey_line"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/totalLabel" />

                        <androidx.fragment.app.FragmentContainerView
                            android:id="@+id/fragment_container_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingBottom="70dp"
                            app:layout_constraintTop_toBottomOf="@id/dividerViewThree" />


                    </androidx.constraintlayout.widget.ConstraintLayout>


                </RelativeLayout>
            </androidx.core.widget.NestedScrollView>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/payContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:background="@color/appBackground"
                android:paddingBottom="@dimen/margin_default"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <View
                    android:id="@+id/dividerViewFive"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/grey_line"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <ImageView
                    android:id="@+id/ad_thumb_view2"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_marginStart="@dimen/margin_default"
                    android:layout_marginTop="18dp"
                    android:background="#F2F2F2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_no_picture" />

                <ImageView
                    android:id="@+id/stockIcon2"
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="@dimen/margin_default"
                    android:layout_marginTop="28dp"
                    app:layout_constraintStart_toEndOf="@+id/ad_thumb_view2"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_stock"
                    app:tint="@color/red_stats_color" />

                <TextView
                    android:id="@+id/stock2"
                    style="@style/OrionTextExtra"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="28dp"
                    android:fontFamily="@font/rubik"
                    android:gravity="center_vertical"
                    android:text="10 Article en stock"
                    android:textColor="@color/orionBlack"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toStartOf="@id/view2"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/stockIcon2"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/adTitleText2"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/margin_default"
                    android:layout_marginTop="@dimen/tiny_spacing"
                    android:layout_marginEnd="5dp"
                    android:fontFamily="@font/rubik"
                    android:maxEms="12"
                    android:text="Fiat 500 Essence Fiat 500 Essence Fiat 500 Essence Fiat 500 Essence"
                    android:textColor="@color/orionBlack"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toStartOf="@id/view2"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@id/ad_thumb_view2"
                    app:layout_constraintTop_toBottomOf="@id/stock2" />

                <View
                    android:id="@+id/view2"
                    android:layout_width="1dp"
                    android:layout_height="50dp"
                    android:background="@color/grey_line"
                    app:layout_constraintBottom_toBottomOf="@id/totalPrice"
                    app:layout_constraintEnd_toStartOf="@+id/totalPrice"
                    app:layout_constraintStart_toEndOf="@+id/adTitleText2"
                    app:layout_constraintTop_toTopOf="@id/adTitleText2" />

                <TextView
                    android:id="@+id/totalPriceLabel"
                    style="@style/OrionTextExtra"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="34dp"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:fontFamily="@font/rubik"
                    android:gravity="center_vertical"
                    android:text="@string/price"
                    android:textColor="@color/orionBlack"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/view2"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/totalPrice"
                    style="@style/OrionTextExtra"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="@dimen/tiny_spacing"
                    android:layout_marginEnd="@dimen/margin_big"
                    android:fontFamily="@font/rubik"
                    android:gravity="center_vertical"
                    android:text="99,000 DH"
                    android:textColor="@color/orionDodgerBlue"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/view2"
                    app:layout_constraintTop_toBottomOf="@id/totalPriceLabel" />


                <com.google.android.material.button.MaterialButton
                    android:id="@+id/payButton"
                    style="@style/OrionButtonPrimary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/margin_default"
                    android:layout_marginTop="@dimen/margin_default"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:layout_marginBottom="@dimen/margin_default"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="@string/goto_payment"
                    android:visibility="gone"
                    app:icon="@drawable/ic_pay_with_card"
                    app:iconGravity="textStart"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ad_thumb_view2" />


                <com.google.android.material.button.MaterialButton
                    android:id="@+id/continueButton"
                    style="@style/OrionButtonPrimary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/margin_default"
                    android:layout_marginTop="@dimen/margin_default"
                    android:layout_marginEnd="@dimen/margin_default"
                    android:layout_marginBottom="@dimen/margin_default"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="@string/vas_continue_steps"
                    app:iconGravity="textStart"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ad_thumb_view2" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </RelativeLayout>

        <include
            android:id="@+id/layout_error"
            layout="@layout/mc_inbox_error_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />
    </FrameLayout>

</LinearLayout>