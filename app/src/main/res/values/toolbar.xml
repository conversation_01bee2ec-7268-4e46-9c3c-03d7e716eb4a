<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">?colorPrimary</item>
        <item name="android:elevation" tools:targetApi="lollipop">4dp</item>
        <item name="titleTextAppearance">@style/ToolbarTextAppearance.Title</item>
        <item name="subtitleTextAppearance">@style/ToolbarTextAppearance.Subtitle</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
    </style>

    <style name="ToolbarTextAppearance.Title" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">16sp</item>
        <!--<item name="android:textStyle">bold</item>-->
    </style>

    <style name="ToolbarTextAppearance.Subtitle" parent="TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
        <item name="android:textSize">12sp</item>
    </style>

</resources>
