<resources>
    <style name="Theme.App.Starting" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/splashColor</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_splash_screen</item>
        <item name="postSplashScreenTheme">@style/Theme.MaterialComponents.DayNight.NoActionBar</item>
    </style>

    <style name="Theme.Avito" parent="@style/Theme.MaterialComponents.DayNight.NoActionBar" />

    <style name="ToolbarTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:textColorPrimary">@color/gray</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
        <item name="android:background">@color/action_bar_color</item>
    </style>

    <style name="Toolbar.TitleText" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/rubik_bold</item>
    </style>

    <style name="DrawerArrowStyle" parent="Widget.AppCompat.DrawerArrowToggle">
        <item name="spinBars">true</item>
        <item name="color">@android:color/black</item>
    </style>
</resources>
