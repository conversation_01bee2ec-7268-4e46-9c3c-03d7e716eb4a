<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- DEV KEYS -->
    <!--<string name="com_appboy_api_key" translatable="false">6926029b-43f5-4b7a-b7c0-86ed72326300</string>-->
    <!--<string name="com_appboy_push_gcm_sender_id" translatable="false">104390722042</string>-->

    <!-- PROD KEYS -->
    <string name="com_braze_api_key" translatable="false">ff16d665-6a91-40e0-bedb-ac9a654c90f6</string>
    <string translatable="false" name="com_braze_firebase_cloud_messaging_sender_id">647240640772</string>
    <bool name="com_braze_handle_push_deep_links_automatically">true</bool>
    <bool name="com_braze_enable_location_collection">true</bool>
    <bool name="com_braze_firebase_cloud_messaging_registration_enabled">true</bool>
    <drawable name="com_braze_push_large_notification_icon">@drawable/avito_logo</drawable>
    <drawable name="com_braze_push_small_notification_icon">@drawable/avito_logo</drawable>
    <bool name="com_braze_push_deep_link_back_stack_activity_enabled">true</bool>
    <string name="com_braze_push_deep_link_back_stack_activity_class_name" translatable="false">se.scmv.morocco.activities.MainActivity</string>
</resources>