<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->

    <dimen name="app_spacing">16dp</dimen>


    <dimen name="space_super_tiny">4dp</dimen>
    <dimen name="space_tiny">4dp</dimen>
    <dimen name="space_small">8dp</dimen>
    <dimen name="space_normal">16dp</dimen>
    <dimen name="space_big">24dp</dimen>
    <dimen name="space_bigger">32dp</dimen>
    <dimen name="space_huge">40dp</dimen>
    <dimen name="ef_item_padding">1dp</dimen>


    <dimen name="ef_padding_small">5dp</dimen>
    <dimen name="ef_padding_medium">10dp</dimen>

    <dimen name="ef_spacing_half">4dp</dimen>
    <dimen name="ef_spacing">8dp</dimen>
    <dimen name="ef_spacing_double">16dp</dimen>

    <dimen name="ef_margin_small">5dp</dimen>
    <dimen name="ef_margin_medium">10dp</dimen>
    <dimen name="ef_margin_large">15dp</dimen>

    <dimen name="ef_font_small">12sp</dimen>
    <dimen name="ef_font_medium">16sp</dimen>

    <dimen name="ef_height_snackbar">80dp</dimen>

    <dimen name="ef_toolbar_elevation">6dp</dimen>
    <dimen name="text_extra_spacing_tiny">2sp</dimen>
    <dimen name="text_extra_spacing_small">4sp</dimen>
    <dimen name="text_extra_spacing_normal">6sp</dimen>
    <dimen name="text_extra_spacing_big">8sp</dimen>

    <dimen name="margin_tiny">4dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_small_plus">10dp</dimen>
    <dimen name="margin_normal">12dp</dimen>
    <dimen name="margin_default">16dp</dimen>
    <dimen name="margin_big">20dp</dimen>
    <dimen name="margin_huge">40dp</dimen>

    <dimen name="padding_default">16dp</dimen>
    <dimen name="padding_large">32dp</dimen>
    <dimen name="padding_medium">24dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="padding_tiny">4dp</dimen>
    <dimen name="padding_xlarge">48dp</dimen>
    <dimen name="padding_xxlarge">54dp</dimen>
    <dimen name="padding_xxxlarge">72dp</dimen>

    <dimen name="text_size_tiny">10sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <dimen name="text_size_normal">16sp</dimen>
    <dimen name="text_size_big">24sp</dimen>
    <dimen name="text_size_bigger">32sp</dimen>
    <dimen name="text_size_huge">72sp</dimen>

    <dimen name="icon_size_thin">16dp</dimen>
    <dimen name="icon_size_small">24dp</dimen>
    <dimen name="icon_size_normal">48dp</dimen>

    <dimen name="regular_button_height">40dp</dimen>
    <dimen name="avito_max_button_width">328dp</dimen>
    <dimen name="avito_button_radius">8dp</dimen>

    <dimen name="cardview_radius">4dp</dimen>
    <dimen name="cardview_elevation">4dp</dimen>


    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>

    <!-- ads listing -->
    <dimen name="listing_item_width">280dp</dimen>
    <dimen name="listing_item_height">120dp</dimen>
    <dimen name="ad_cardview_btn_size">28dp</dimen>
    <dimen name="ad_listview_btn_size">24dp</dimen>
    <dimen name="ad_cardview_btn_mrgn">4dp</dimen>
    <dimen name="ad_title_size">12sp</dimen>
    <dimen name="ad_content_size">11sp</dimen>
    <dimen name="ad_price_width">80dp</dimen>
    <dimen name="pic_list_width">130dp</dimen>
    <dimen name="card_view_corder_radius">4dp</dimen>

    <!-- ads search filters -->
    <dimen name="spinner_field_height">35dp</dimen>
    <dimen name="spinner_town_field_height">40dp</dimen>
    <dimen name="spinner_height">60dp</dimen>
    <dimen name="search_filters_width">260dp</dimen>
    <dimen name="search_filters_search_textsize">14sp</dimen>

    <!-- no favoris/resultat/save search -->
    <dimen name="no_results_text_size">14sp</dimen>
    <dimen name="no_results_btn_size_height">55dp</dimen>
    <dimen name="no_results_btn_size_width">150dp</dimen>

    <!-- ad insertion -->
    <dimen name="ai_fields_text_size">14sp</dimen>
    <dimen name="ai_pic_width">84dp</dimen>
    <dimen name="ai_pic_height">84dp</dimen>

    <!-- vas ad insertion -->
    <dimen name="vas_ai_mayout_padding">16dp</dimen>
    <dimen name="vas_ai_card_height">120dp</dimen>
    <dimen name="vas_ai_card_elevation">4dp</dimen>
    <dimen name="vas_ai_card_corner_radius">4dp</dimen>
    <dimen name="vas_ai_card_radio_margin">8dp</dimen>
    <dimen name="vas_ai_package_image_dimen">70dp</dimen>
    <dimen name="vas_ai_packages_space">16dp</dimen>


    <!-- vas ad insertion detail-->
    <dimen name="vas_ai_package_detail_image_dimen">100dp</dimen>
    <dimen name="card_margin">8dp</dimen>
    <dimen name="fab_margin">8dp</dimen>
    <dimen name="minimum_vas_ai_package_height">100dp</dimen>


    <!-- ad Detail -->
    <dimen name="detail_backdrop_height">340dp</dimen>

    <dimen name="margin_bar_price">95dp</dimen>
    <dimen name="margin_bar_price_s">75dp</dimen>

    <dimen name="collapse_bar_title">80dp</dimen>
    <dimen name="collapse_bar_title_s">60dp</dimen>

    <dimen name="ad_title_textview">7dp</dimen>


    <dimen name="textview_drawable_padding">8dp</dimen>

    <dimen name="ads_grid_item_margin">8dp</dimen>
    <dimen name="ad_status_rectangle_corner">4dp</dimen>
    <dimen name="tendp">10dp</dimen>


    <dimen name="uploaded_image_spacing">8dp</dimen>
    <dimen name="image_upload_item_size">104dp</dimen>
    <dimen name="image_upload_icon_size">48dp</dimen>

    <!-- image size-->
    <dimen name="image_size_normal">32dp</dimen>

    <dimen name="vas_gallery_card_height">140dp</dimen>
    <dimen name="vas_gallery_card_width">149dp</dimen>
    <dimen name="drawer_menu_height">180dp</dimen>
    <!-- Orion dimens-->
    <dimen name="orion_input_field_height">40dp</dimen>
    <dimen name="orion_icon_field_size">16dp</dimen>
    <dimen name="orion_icon_field_medium_size">24dp</dimen>
    <dimen name="orion_icon_action_size">24dp</dimen>
    <dimen name="orion_spinner_height">48dp</dimen>
    <dimen name="orion_button_text_size">14sp</dimen>
    <dimen name="orion_button_height">40dp</dimen>
    <dimen name="northstar_button_radius">4dp</dimen>
    <dimen name="text_size_menu_item">14sp</dimen>
    <dimen name="max_width_nav_view">320dp</dimen>

    <dimen name="message_bubble_radius">8dp</dimen>
    <dimen name="avatar_bubble_size">28dp</dimen>
    <dimen name="default_spacing">16dp</dimen>
    <dimen name="half_spacing">8dp</dimen>
    <dimen name="tiny_spacing">4dp</dimen>
    <dimen name="message_metadata_text_size">12sp</dimen>
    <dimen name="text_size">16sp</dimen>
    <dimen name="touching_point_height">70dp</dimen>

    <dimen name="expandable_card_stats">150dp</dimen>
    <dimen name="default_card_height">65dp</dimen>
    <dimen name="default_vertical_padding">10dp</dimen>

    <dimen name="loader_width">172dp</dimen>
    <dimen name="loader_height">2dp</dimen>


    <!-- Sizes for a square button with a system icon on it -->
    <dimen name="square_button_icon_size">24dp</dimen>
    <dimen name="square_button_size">48dp</dimen>
    <dimen name="square_button_padding">10dp</dimen>

    <dimen name="suggestion_body_text_size">18sp</dimen>
    <dimen name="search_bar_text_size">16sp</dimen>

    <dimen name="search_bar_right_icon_right_margin">4dp</dimen>
    <dimen name="search_view_corner_radius">3dp</dimen>

</resources>
