<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string name="regex_minimum_two_characters" translatable="false">.{2,}</string>
    <string name="regex_minimum_ten_characters" translatable="false">.{10,}</string>
    <string name="regex_minimum_five_characters" translatable="false">.{5,}</string>
    <string name="regex_email" translatable="false">^[A-Za-z0-9+_.-]+@(.+)$</string>
    <string name="regex_phone_number" translatable="false">^(05|06|07)\\d{8}</string>
    <string name="email_validation_pattern" translatable="false">^[a-zA-Z0-9\\+\\.\\_\\%\\-\\+]{1,256}\\@[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}(\\.[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25})</string>
</resources>